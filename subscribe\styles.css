/* ReplyPal - Subscription Page Styles */

:root {
  --primary-color: #4a6cf7;
  --primary-hover: #3a5ce5;
  --primary-light: #eef1ff;
  --secondary-color: #f5f7ff;
  --text-color: #333;
  --light-text: #666;
  --label-text: #444;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --error-color: #f44336;
  --warning-color: #ff9800;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
  --transition: all 0.2s ease;
  --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

/* Reset & Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: var(--font-family);
  font-size: 16px;
  line-height: 1.5;
  color: var(--text-color);
  background-color: #f9faff;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Header Styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 5%;
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-logo i {
  font-size: 24px;
}

.header-logo h1 {
  font-size: 24px;
  font-weight: 600;
}

.header-nav {
  display: flex;
  align-items: center;
}

.login-btn {
  display: inline-block;
  padding: 8px 16px;
  background-color: white;
  color: var(--primary-color);
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
}

.login-btn:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 40px 5%;
}

.hero-section {
  text-align: center;
  margin-bottom: 50px;
}

.hero-section h2 {
  font-size: 32px;
  margin-bottom: 15px;
  color: var(--text-color);
}

.hero-section p {
  font-size: 18px;
  color: var(--light-text);
  max-width: 600px;
  margin: 0 auto;
}

/* Pricing Section */
.pricing-section {
  margin-bottom: 60px;
}

.pricing-cards {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.pricing-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: var(--shadow);
  overflow: hidden;
  width: 300px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.pricing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.pricing-card.premium {
  border: 2px solid var(--primary-color);
}

.ribbon {
  position: absolute;
  top: 20px;
  right: -30px;
  background-color: var(--primary-color);
  color: white;
  padding: 5px 30px;
  transform: rotate(45deg);
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.card-header {
  padding: 25px 20px;
  text-align: center;
  border-bottom: 1px solid var(--border-color);
}

.card-header h3 {
  font-size: 22px;
  margin-bottom: 10px;
}

.price {
  font-size: 36px;
  font-weight: 700;
  color: var(--primary-color);
}

.price span {
  font-size: 16px;
  font-weight: 400;
  color: var(--light-text);
}

.card-body {
  padding: 25px 20px;
}

.features-list {
  list-style: none;
  margin-bottom: 25px;
}

.features-list li {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.features-list i {
  color: var(--success-color);
  font-size: 14px;
}

.btn {
  display: block;
  width: 100%;
  padding: 12px;
  border-radius: var(--border-radius);
  font-family: var(--font-family);
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
  transition: var(--transition);
  border: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--light-text);
}

.btn-outline:hover:not([disabled]) {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn[disabled] {
  opacity: 0.7;
  cursor: not-allowed;
}

/* FAQ Section */
.faq-section {
  max-width: 800px;
  margin: 0 auto 60px;
}

.faq-section h2 {
  text-align: center;
  margin-bottom: 30px;
  font-size: 28px;
}

.faq-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.faq-item {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.faq-question {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.faq-question h3 {
  font-size: 18px;
  font-weight: 500;
}

.faq-answer {
  padding: 0 20px;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
}

.faq-item.active .faq-answer {
  padding: 0 20px 20px;
  max-height: 200px;
}

.faq-item.active .faq-question i {
  transform: rotate(180deg);
}

/* Footer */
.footer {
  background-color: #f0f2ff;
  padding: 20px 5%;
  border-top: 1px solid var(--border-color);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-links {
  display: flex;
  gap: 20px;
}

.footer-links a {
  color: var(--light-text);
  text-decoration: none;
  transition: var(--transition);
}

.footer-links a:hover {
  color: var(--primary-color);
}

/* Toast notification */
.toast {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 24px;
  border-radius: 4px;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.3s;
  z-index: 1000;
}

.toast.show {
  opacity: 1;
}

.toast.error {
  background-color: rgba(220, 53, 69, 0.9);
}

.toast.success {
  background-color: rgba(40, 167, 69, 0.9);
}

/* Loading overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.loading-overlay.show {
  opacity: 1;
  visibility: visible;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(74, 108, 247, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Content Section Styles for Stripe Requirements */
.content-section {
  max-width: 900px;
  margin: 0 auto 60px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.content-card {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 25px;
}

.content-card h3 {
  font-size: 22px;
  margin-bottom: 15px;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.content-card h4 {
  font-size: 18px;
  margin: 20px 0 10px;
  color: var(--text-color);
}

.content-card p {
  margin-bottom: 15px;
  color: var(--light-text);
}

.requirements-list {
  list-style: none;
  margin: 15px 0 20px;
  padding-left: 10px;
}

.requirements-list li {
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.requirements-list i.fa-check {
  color: var(--success-color);
  font-size: 14px;
  margin-top: 4px;
}

.requirements-list i.fa-times {
  color: var(--error-color);
  font-size: 14px;
  margin-top: 4px;
}

.cta-section {
  text-align: center;
  background-color: var(--primary-light);
  padding: 40px;
  border-radius: var(--border-radius);
  margin-bottom: 60px;
}

.cta-section h3 {
  font-size: 24px;
  margin-bottom: 15px;
}

.cta-section p {
  margin-bottom: 25px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-section .btn {
  display: inline-block;
  width: auto;
  min-width: 200px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .pricing-cards {
    flex-direction: column;
    align-items: center;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .footer-links {
    justify-content: center;
  }

  .hero-section h2 {
    font-size: 28px;
  }

  .hero-section p {
    font-size: 16px;
  }

  .content-card {
    padding: 20px;
  }

  .cta-section {
    padding: 30px 20px;
  }
}
