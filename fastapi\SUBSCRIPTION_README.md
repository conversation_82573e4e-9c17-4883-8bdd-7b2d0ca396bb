# ReplyPal Subscription System

This document provides information on how to set up and use the Stripe subscription system for ReplyPal.

## Overview

The ReplyPal subscription system uses Stripe to manage subscription plans, payments, and usage limits. The system includes:

- Multiple subscription tiers (Free, Basic)
- Usage limits based on subscription tier
- Stripe Checkout for payment processing
- Stripe Customer Portal for subscription management
- Webhook handling for subscription events

## Setup Instructions

### 1. Create a Stripe Account

If you don't already have a Stripe account, sign up at [stripe.com](https://stripe.com).

### 2. Set Up Stripe Products and Prices

1. In your Stripe Dashboard, go to Products > Create Product
2. Create the following product and price:

   **Basic Plan**

   - Name: Basic Plan
   - Description: Standard access for individual users
   - Price: $9.99/month (recurring)
   - Save the Price ID for the .env file

### 3. Set Up Stripe Webhook

1. In your Stripe Dashboard, go to Developers > Webhooks > Add Endpoint
2. Enter your webhook URL (e.g., `https://your-api.com/subscription/webhook`)
3. Select the following events to listen for:
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
4. Save the webhook and note the Signing Secret for your .env file

### 4. Configure Environment Variables

Copy the `.env.example` file to `.env` and update the following variables:

```
# Stripe settings
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# Stripe price IDs for subscription tiers
STRIPE_BASIC_PRICE_ID=price_basic_id

# Frontend URLs for Stripe redirects
FRONTEND_SUCCESS_URL=http://your-frontend.com/success
FRONTEND_CANCEL_URL=http://your-frontend.com/cancel

# AWS DynamoDB settings
AWS_REGION=your-aws-region
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
# For local development
# DYNAMODB_ENDPOINT=http://localhost:8000
```

### 5. Install Dependencies

Install the required dependencies:

```bash
pip install -r requirements.txt
```

### 6. Set Up DynamoDB

#### Option 1: Use AWS DynamoDB

1. Create an AWS account if you don't have one
2. Create the following DynamoDB tables in your AWS account:
   - `replypal-customers` (Primary key: `id` - String)
   - `replypal-subscriptions` (Primary key: `id` - String)
   - `replypal-user-subscriptions` (Primary key: `user_id` - String)
   - `replypal-usage-records` (Primary key: `id` - String)
3. Create an IAM user with permissions to access these tables
4. Update your `.env` file with the AWS credentials:
   ```
   AWS_REGION=your-aws-region
   AWS_ACCESS_KEY_ID=your-aws-access-key-id
   AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
   ```

#### Option 2: Use DynamoDB Local for Development

1. Download and run DynamoDB Local:

   ```bash
   # Download DynamoDB Local
   mkdir -p dynamodb-local
   cd dynamodb-local
   wget https://d1ni2b6xgvw0s0.cloudfront.net/amazon-dynamodb-local-latest.tar.gz
   tar -xzf amazon-dynamodb-local-latest.tar.gz

   # Run DynamoDB Local
   java -Djava.library.path=./DynamoDBLocal_lib -jar DynamoDBLocal.jar -sharedDb
   ```

2. Create the required tables using AWS CLI:

   ```bash
   # Create customers table
   aws dynamodb create-table \
     --table-name replypal-customers \
     --attribute-definitions AttributeName=id,AttributeType=S \
     --key-schema AttributeName=id,KeyType=HASH \
     --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
     --endpoint-url http://localhost:8000

   # Create subscriptions table
   aws dynamodb create-table \
     --table-name replypal-subscriptions \
     --attribute-definitions AttributeName=id,AttributeType=S \
     --key-schema AttributeName=id,KeyType=HASH \
     --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
     --endpoint-url http://localhost:8000

   # Create user subscriptions table
   aws dynamodb create-table \
     --table-name replypal-user-subscriptions \
     --attribute-definitions AttributeName=user_id,AttributeType=S \
     --key-schema AttributeName=user_id,KeyType=HASH \
     --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
     --endpoint-url http://localhost:8000

   # Create usage records table
   aws dynamodb create-table \
     --table-name replypal-usage-records \
     --attribute-definitions AttributeName=id,AttributeType=S \
     --key-schema AttributeName=id,KeyType=HASH \
     --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
     --endpoint-url http://localhost:8000
   ```

3. Update your `.env` file to use DynamoDB Local:
   ```
   AWS_REGION=us-east-1
   DYNAMODB_ENDPOINT=http://localhost:8000
   ```

## API Endpoints

### Get Subscription Plans

```
GET /subscription/plans
```

Returns a list of available subscription plans with details.

### Get Customer Information

```
GET /subscription/customer
```

Returns information about the current user's subscription status.

### Create Checkout Session

```
POST /subscription/create-checkout-session
```

Creates a Stripe Checkout session for subscription payment.

**Request Body:**

```json
{
  "price_id": "price_1234567890"
}
```

### Create Customer Portal Session

```
POST /subscription/customer-portal
```

Creates a Stripe Customer Portal session for subscription management.

### Cancel Subscription

```
POST /subscription/cancel
```

Cancels the current user's subscription.

### Get Usage Statistics

```
GET /subscription/usage
```

Returns usage statistics for the current user.

## Subscription Tiers and Limits

| Tier  | Price | Requests/Day | Max Tokens | Features                      |
| ----- | ----- | ------------ | ---------- | ----------------------------- |
| Free  | $0    | 5            | 500        | Basic support                 |
| Basic | $9.99 | 50           | 1,000      | Email support, History saving |

## Testing Stripe Integration

For testing, you can use Stripe's test mode and test cards:

- Test card number: `4242 4242 4242 4242`
- Expiration date: Any future date
- CVC: Any 3 digits
- ZIP: Any 5 digits

For testing webhooks locally, you can use the Stripe CLI:

```bash
stripe listen --forward-to http://localhost:8000/subscription/webhook
```

## Troubleshooting

- **Webhook Issues**: Check the Stripe Dashboard for webhook delivery attempts and errors
- **Payment Issues**: Check the Stripe Dashboard for payment logs and error messages
- **Database Issues**: Check MongoDB connection and database permissions
