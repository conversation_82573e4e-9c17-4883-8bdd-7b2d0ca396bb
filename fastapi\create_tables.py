"""
<PERSON><PERSON><PERSON> to create DynamoDB tables for ReplyPal API
Run this script to create the required tables if they don't exist
"""

import os
import boto3
from botocore.exceptions import ClientError
import time

# AWS DynamoDB settings
AWS_REGION = os.getenv("AWS_REGION", "us-east-1")
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
DYNAMODB_ENDPOINT = os.getenv("DYNAMODB_ENDPOINT")  # For local development

# Table names
CUSTOMERS_TABLE = os.getenv("DYNAMODB_CUSTOMERS_TABLE", "replypal-customers")
SUBSCRIPTIONS_TABLE = os.getenv("DYNAMODB_SUBSCRIPTIONS_TABLE", "replypal-subscriptions")
USER_SUBSCRIPTIONS_TABLE = os.getenv("DYNAMODB_USER_SUBSCRIPTIONS_TABLE", "replypal-user-subscriptions")
USAGE_RECORDS_TABLE = os.getenv("DYNAMODB_USAGE_RECORDS_TABLE", "replypal-usage-records")

# When using a profile, we don't need to explicitly set access key and secret
# The profile will handle authentication
if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY:
    print("WARNING: AWS credentials are set in environment variables but will be ignored in favor of the sumathy_aws profile.")
    print("To use environment variables instead of a profile, remove the profile_name from dynamodb_kwargs.")

# Initialize kwargs for resource (without profile)
dynamodb_kwargs = {}
if DYNAMODB_ENDPOINT:
    dynamodb_kwargs['endpoint_url'] = DYNAMODB_ENDPOINT

# Initialize DynamoDB client
try:
    # Create a session with the profile
    session = boto3.Session(profile_name='sumathy_aws')
    # Use the session to create resources
    dynamodb = session.resource('dynamodb', **dynamodb_kwargs)
    dynamodb_client = session.client('dynamodb', **dynamodb_kwargs)
    print(f"Successfully connected to DynamoDB using profile 'sumathy_aws'")
except Exception as e:
    print(f"ERROR connecting to DynamoDB: {str(e)}")
    exit(1)

def table_exists(table_name):
    """Check if a table exists"""
    try:
        dynamodb_client.describe_table(TableName=table_name)
        return True
    except ClientError as e:
        if e.response['Error']['Code'] == 'ResourceNotFoundException':
            return False
        raise

def create_customers_table():
    """Create the customers table"""
    if table_exists(CUSTOMERS_TABLE):
        print(f"Table {CUSTOMERS_TABLE} already exists")
        return

    print(f"Creating table {CUSTOMERS_TABLE}...")
    table = dynamodb.create_table(
        TableName=CUSTOMERS_TABLE,
        KeySchema=[
            {
                'AttributeName': 'id',
                'KeyType': 'HASH'  # Partition key
            }
        ],
        AttributeDefinitions=[
            {
                'AttributeName': 'id',
                'AttributeType': 'S'
            }
        ],
        ProvisionedThroughput={
            'ReadCapacityUnits': 5,
            'WriteCapacityUnits': 5
        }
    )

    # Wait for table creation
    table.meta.client.get_waiter('table_exists').wait(TableName=CUSTOMERS_TABLE)
    print(f"Table {CUSTOMERS_TABLE} created successfully")

def create_subscriptions_table():
    """Create the subscriptions table"""
    if table_exists(SUBSCRIPTIONS_TABLE):
        print(f"Table {SUBSCRIPTIONS_TABLE} already exists")
        return

    print(f"Creating table {SUBSCRIPTIONS_TABLE}...")
    table = dynamodb.create_table(
        TableName=SUBSCRIPTIONS_TABLE,
        KeySchema=[
            {
                'AttributeName': 'id',
                'KeyType': 'HASH'  # Partition key
            }
        ],
        AttributeDefinitions=[
            {
                'AttributeName': 'id',
                'AttributeType': 'S'
            },
            {
                'AttributeName': 'customer_id',
                'AttributeType': 'S'
            }
        ],
        GlobalSecondaryIndexes=[
            {
                'IndexName': 'CustomerIdIndex',
                'KeySchema': [
                    {
                        'AttributeName': 'customer_id',
                        'KeyType': 'HASH'
                    }
                ],
                'Projection': {
                    'ProjectionType': 'ALL'
                },
                'ProvisionedThroughput': {
                    'ReadCapacityUnits': 5,
                    'WriteCapacityUnits': 5
                }
            }
        ],
        ProvisionedThroughput={
            'ReadCapacityUnits': 5,
            'WriteCapacityUnits': 5
        }
    )

    # Wait for table creation
    table.meta.client.get_waiter('table_exists').wait(TableName=SUBSCRIPTIONS_TABLE)
    print(f"Table {SUBSCRIPTIONS_TABLE} created successfully")

def create_user_subscriptions_table():
    """Create the user subscriptions table"""
    if table_exists(USER_SUBSCRIPTIONS_TABLE):
        print(f"Table {USER_SUBSCRIPTIONS_TABLE} already exists")
        return

    print(f"Creating table {USER_SUBSCRIPTIONS_TABLE}...")
    table = dynamodb.create_table(
        TableName=USER_SUBSCRIPTIONS_TABLE,
        KeySchema=[
            {
                'AttributeName': 'user_id',
                'KeyType': 'HASH'  # Partition key
            }
        ],
        AttributeDefinitions=[
            {
                'AttributeName': 'user_id',
                'AttributeType': 'S'
            }
        ],
        ProvisionedThroughput={
            'ReadCapacityUnits': 5,
            'WriteCapacityUnits': 5
        }
    )

    # Wait for table creation
    table.meta.client.get_waiter('table_exists').wait(TableName=USER_SUBSCRIPTIONS_TABLE)
    print(f"Table {USER_SUBSCRIPTIONS_TABLE} created successfully")

def create_usage_records_table():
    """Create the usage records table"""
    if table_exists(USAGE_RECORDS_TABLE):
        print(f"Table {USAGE_RECORDS_TABLE} already exists")
        return

    print(f"Creating table {USAGE_RECORDS_TABLE}...")
    table = dynamodb.create_table(
        TableName=USAGE_RECORDS_TABLE,
        KeySchema=[
            {
                'AttributeName': 'id',
                'KeyType': 'HASH'  # Partition key
            }
        ],
        AttributeDefinitions=[
            {
                'AttributeName': 'id',
                'AttributeType': 'S'
            },
            {
                'AttributeName': 'user_id',
                'AttributeType': 'S'
            }
        ],
        GlobalSecondaryIndexes=[
            {
                'IndexName': 'UserIdIndex',
                'KeySchema': [
                    {
                        'AttributeName': 'user_id',
                        'KeyType': 'HASH'
                    }
                ],
                'Projection': {
                    'ProjectionType': 'ALL'
                },
                'ProvisionedThroughput': {
                    'ReadCapacityUnits': 5,
                    'WriteCapacityUnits': 5
                }
            }
        ],
        ProvisionedThroughput={
            'ReadCapacityUnits': 5,
            'WriteCapacityUnits': 5
        }
    )

    # Wait for table creation
    table.meta.client.get_waiter('table_exists').wait(TableName=USAGE_RECORDS_TABLE)
    print(f"Table {USAGE_RECORDS_TABLE} created successfully")

def main():
    """Create all tables"""
    print("Creating DynamoDB tables for ReplyPal API...")

    # Create tables
    create_customers_table()
    create_subscriptions_table()
    create_user_subscriptions_table()
    create_usage_records_table()

    print("All tables created successfully")

if __name__ == "__main__":
    main()
