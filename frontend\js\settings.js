/**
 * ReplyPal - Settings Management
 * Handles loading, saving, and managing settings
 */

// DOM Elements
const environmentSelect = document.getElementById('environment');
const currentApiUrlSpan = document.getElementById('current-api-url');
const testApiBtn = document.getElementById('test-api-btn');
const apiTestResult = document.getElementById('api-test-result');
const saveHistoryCheckbox = document.getElementById('save-history');
const useMockApiCheckbox = document.getElementById('use-mock-api');
const saveSettingsBtn = document.getElementById('save-settings-btn');
const apiSettingsHeading = document.querySelector('#settings .settings-container h3:nth-of-type(3)');
const apiSettingsFormGroup = document.querySelector('#settings .settings-container .form-group:nth-of-type(3)');
const toggleHotkeysBtn = document.getElementById('toggle-hotkeys-btn');
const additionalHotkeys = document.getElementById('additional-hotkeys');
const usageProgress = document.getElementById('usage-progress');
const remainingResponses = document.getElementById('remaining-responses');
const totalResponses = document.getElementById('total-responses');
const upgradeLink = document.getElementById('upgrade-link');

// Default settings
const defaultSettings = {
  environment: 'local', //local, dev, prod
  saveHistory: true,
  useMockApi: false
};

// API URLs for different environments
const apiUrls = {
  local: 'http://localhost:8000',
  dev: 'https://dev-api.replypal.com',
  prod: 'https://api.replypal.com'
};

// Subscription URL
const subscriptionUrl = 'http://replypal-subscription.s3-website-us-east-1.amazonaws.com/index.html';

// Set up event listeners
document.addEventListener('DOMContentLoaded', () => {
  // Call toggleApiSettingsVisibility on page load
  toggleApiSettingsVisibility();

  // Environment selection change
  if (environmentSelect) {
    environmentSelect.addEventListener('change', updateApiUrlDisplay);
  }

  // Test API button
  if (testApiBtn) {
    testApiBtn.addEventListener('click', testApiConnection);
  }

  // Save settings button
  if (saveSettingsBtn) {
    saveSettingsBtn.addEventListener('click', saveSettings);
  }

  // Toggle hotkeys button
  if (toggleHotkeysBtn && additionalHotkeys) {
    toggleHotkeysBtn.addEventListener('click', toggleHotkeysVisibility);
  }

  // Upgrade link
  if (upgradeLink) {
    upgradeLink.addEventListener('click', handleUpgradeClick);
  }

  // Fetch usage information
  fetchUsageInfo();
});

/**
 * Load settings from storage
 */
function loadSettings() {
  chrome.storage.local.get('settings', (data) => {
    const settings = data.settings || defaultSettings;

    // Set environment
    if (environmentSelect) {
      environmentSelect.value = settings.environment || 'local';
      updateApiUrlDisplay();
    }

    // Set other settings
    if (saveHistoryCheckbox) {
      saveHistoryCheckbox.checked = settings.saveHistory !== false;
    }

    if (useMockApiCheckbox) {
      useMockApiCheckbox.checked = settings.useMockApi === true;
    }
  });
}

/**
 * Toggle API settings visibility based on default environment
 * Hide API settings section when default environment is set to production
 */
function toggleApiSettingsVisibility() {
  if (!apiSettingsHeading || !apiSettingsFormGroup) return;

  const isProdEnvironment = defaultSettings.environment === 'prod';

  // Hide or show API settings section based on default environment
  if (isProdEnvironment) {
    // Hide API settings in production environment
    apiSettingsHeading.style.display = 'none';
    apiSettingsFormGroup.style.display = 'none';
  } else {
    // Show API settings in non-production environments
    apiSettingsHeading.style.display = '';
    apiSettingsFormGroup.style.display = '';
  }
}

/**
 * Update API URL display based on selected environment
 */
function updateApiUrlDisplay() {
  if (currentApiUrlSpan && environmentSelect) {
    const environment = environmentSelect.value;
    currentApiUrlSpan.textContent = getApiUrlForEnvironment(environment);
  }
}

/**
 * Save settings to storage
 */
function saveSettings() {
  const settings = {
    environment: environmentSelect ? environmentSelect.value : 'local',
    saveHistory: saveHistoryCheckbox ? saveHistoryCheckbox.checked : true,
    useMockApi: useMockApiCheckbox ? useMockApiCheckbox.checked : false
  };

  chrome.storage.local.set({ settings: settings }, () => {
    showToast('Settings saved');
  });
}

/**
 * Get settings from storage
 * @returns {Promise<Object>} Settings object
 */
function getSettings() {
  return new Promise((resolve) => {
    chrome.storage.local.get('settings', (data) => {
      resolve(data.settings || defaultSettings);
    });
  });
}

/**
 * Get API URL based on environment
 * @param {string} environment - The environment (local, dev, prod)
 * @returns {string} The API URL for the specified environment
 */
function getApiUrlForEnvironment(environment) {
  return apiUrls[environment] || apiUrls.local;
}

/**
 * Test API connection
 */
async function testApiConnection() {
  if (!apiTestResult) return;

  // Get the API URL
  const environment = environmentSelect ? environmentSelect.value : 'local';
  const apiUrl = getApiUrlForEnvironment(environment);

  // Update the test result
  apiTestResult.textContent = "Testing connection...";
  apiTestResult.style.color = "var(--text-color)";

  try {
    // Test the ping endpoint
    const response = await fetch(`${apiUrl}/ping`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Origin': window.location.origin,
        'X-Requested-With': 'XMLHttpRequest'
      },
      mode: 'cors',
      credentials: 'omit'
    });

    if (response.ok) {
      const data = await response.json();
      apiTestResult.textContent = `Connection successful! API version: ${data.version}`;
      apiTestResult.style.color = "var(--success-color)";

      // Also test the generate endpoint with a simple request
      try {
        const generateResponse = await fetch(`${apiUrl}/generate`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Origin': window.location.origin,
            'X-Requested-With': 'XMLHttpRequest'
          },
          mode: 'cors',
          credentials: 'omit',
          body: JSON.stringify({
            selected_text: "This is a test message.",
            user_intent: "Test the API connection",
            tone: "neutral",
            purpose: "reply"
          })
        });

        if (generateResponse.ok) {
          apiTestResult.textContent += "\nGenerate endpoint is also working!";
        } else {
          apiTestResult.textContent += "\nGenerate endpoint test failed: " +
            generateResponse.status + " " + generateResponse.statusText;
        }
      } catch (generateError) {
        apiTestResult.textContent += "\nGenerate endpoint test error: " + generateError.message;
      }
    } else {
      apiTestResult.textContent = `Connection failed: ${response.status} ${response.statusText}`;
      apiTestResult.style.color = "var(--error-color)";
    }
  } catch (error) {
    // Provide more detailed error information
    let errorMessage = error.message;

    // Check if it's a network error
    if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
      errorMessage = 'Network error: Unable to connect to the API server. Please check if the server is running and accessible.';
    }

    // Check if it's a CORS error
    if (error.message.includes('CORS')) {
      errorMessage = 'CORS error: The API server is not allowing requests from the extension. Please check CORS settings on the server.';
    }

    apiTestResult.textContent = `Connection error: ${errorMessage}`;
    apiTestResult.style.color = "var(--error-color)";
  }
}

/**
 * Toggle visibility of additional hotkeys
 */
function toggleHotkeysVisibility() {
  if (!toggleHotkeysBtn || !additionalHotkeys) return;

  // Toggle the 'hidden' class on the additional hotkeys container
  additionalHotkeys.classList.toggle('hidden');

  // Toggle the 'expanded' class on the button to rotate the icon
  toggleHotkeysBtn.classList.toggle('expanded');

  // Update the button title based on current state
  if (additionalHotkeys.classList.contains('hidden')) {
    toggleHotkeysBtn.title = 'Show all shortcuts';
  } else {
    toggleHotkeysBtn.title = 'Hide additional shortcuts';
  }
}

/**
 * Fetch usage information from the API and update the UI
 */
async function fetchUsageInfo() {
  try {
    // Get user data for authentication
    const userData = await getUserData();

    if (!userData || !userData.apiToken) {
      // User is not logged in, show default values
      updateUsageUI(0, 5);
      return;
    }

    // Get settings for API URL
    const settings = await getSettings();
    const environment = settings.environment || 'local';
    const apiUrl = getApiUrlForEnvironment(environment);

    // Fetch usage information
    const response = await fetch(`${apiUrl}/subscription/usage`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${userData.apiToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      // Error fetching usage info, show default values
      updateUsageUI(0, 5);
      return;
    }

    const usageData = await response.json();
    const dailyUsage = usageData.daily_usage || 0;
    const dailyLimit = usageData.daily_limit || 5;
    const remaining = Math.max(0, dailyLimit - dailyUsage);

    // Update UI with usage information
    updateUsageUI(remaining, dailyLimit);
  } catch (error) {
    console.error('Error fetching usage information:', error);
    // Show default values on error
    updateUsageUI(0, 5);
  }
}

/**
 * Update the usage UI with the provided values
 * @param {number} remaining - Remaining responses
 * @param {number} total - Total allowed responses
 */
function updateUsageUI(remaining, total) {
  if (remainingResponses) {
    remainingResponses.textContent = remaining;
  }

  if (totalResponses) {
    totalResponses.textContent = total;
  }

  if (usageProgress) {
    const percentage = total > 0 ? ((total - remaining) / total) * 100 : 0;
    usageProgress.style.width = `${percentage}%`;

    // Change color based on usage
    if (percentage > 80) {
      usageProgress.style.backgroundColor = '#ff9800'; // Warning color
    } else if (percentage > 95) {
      usageProgress.style.backgroundColor = '#f44336'; // Error color
    } else {
      usageProgress.style.backgroundColor = ''; // Default color
    }
  }
}

/**
 * Handle upgrade link click
 */
async function handleUpgradeClick(e) {
  e.preventDefault();

  try {
    // Get user data
    const userData = await getUserData();

    // Get settings for API URL
    const settings = await getSettings();
    const environment = settings.environment || 'local';

    // If user is logged in, pass login details to subscription page
    if (userData && userData.apiToken) {
      // Store user data in localStorage for the subscription page
      localStorage.setItem('replypal_user', JSON.stringify(userData));
      localStorage.setItem('replypal_environment', environment);
      // Mark that we're coming from the extension
      localStorage.setItem('replypal_from_extension', 'true');
    }

    // Open subscription page in a new tab
    window.open(subscriptionUrl, '_blank');

    // Show toast notification
    showToast('Opening subscription page...', false);
  } catch (error) {
    console.error('Error handling upgrade click:', error);
    showToast('Error opening subscription page', true);
  }
}

/**
 * Get user data from storage
 */
function getUserData() {
  return new Promise((resolve) => {
    chrome.storage.local.get('user', (data) => {
      resolve(data.user || null);
    });
  });
}

/**
 * Show a toast notification
 * @param {string} message - The message to display
 * @param {boolean} isError - Whether this is an error message
 */
function showToast(message, isError = false) {
  const toast = document.getElementById('toast');
  if (!toast) return;

  // Clear any existing timeout
  if (toast.timeoutId) {
    clearTimeout(toast.timeoutId);
  }

  // Set message and class
  toast.textContent = message;
  toast.className = 'toast';
  if (isError) {
    toast.classList.add('error');
  }

  // Show toast
  setTimeout(() => {
    toast.classList.add('show');
  }, 10);

  // Hide toast after 3 seconds
  toast.timeoutId = setTimeout(() => {
    toast.classList.remove('show');
  }, 3000);
}

// ReplyPal Settings API for backward compatibility
const ReplyPalSettings = {
  getApiUrl: function() {
    const environment = environmentSelect ? environmentSelect.value : 'local';
    return getApiUrlForEnvironment(environment);
  },

  setEnvironment: function(environment) {
    if (environmentSelect && apiUrls[environment]) {
      environmentSelect.value = environment;
      updateApiUrlDisplay();
    }
  }
};
