<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>ReplyPal - Smart Email Assistant</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    :root {
      --primary-color: #4a6cf7;
      --primary-hover: #3a5ce5;
      --primary-light: #eef1ff;
      --secondary-color: #f5f7ff;
      --text-color: #333;
      --light-text: #666;
      --label-text: #444;
      --border-color: #e0e0e0;
      --success-color: #4caf50;
      --error-color: #f44336;
      --warning-color: #ff9800;
      --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      --border-radius: 8px;
      --transition: all 0.2s ease;
      --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
        Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: var(--font-family);
      line-height: 1.6;
      color: var(--text-color);
      background-color: var(--secondary-color);
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 20px 0;
      box-shadow: var(--shadow);
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .logo {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 24px;
      font-weight: 700;
    }

    nav {
      display: flex;
      gap: 20px;
    }

    nav a {
      color: white;
      text-decoration: none;
      font-weight: 500;
      transition: var(--transition);
      padding: 5px 10px;
      border-radius: var(--border-radius);
    }

    nav a:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    .hero {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
      color: white;
      padding: 80px 0;
      text-align: center;
    }

    .hero h1 {
      font-size: 48px;
      margin-bottom: 20px;
    }

    .hero p {
      font-size: 20px;
      max-width: 600px;
      margin: 0 auto 30px;
    }

    .btn {
      display: inline-block;
      padding: 12px 24px;
      background-color: white;
      color: var(--primary-color);
      border: none;
      border-radius: var(--border-radius);
      font-weight: 600;
      text-decoration: none;
      transition: var(--transition);
      cursor: pointer;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    section {
      padding: 60px 0;
    }

    section h2 {
      font-size: 32px;
      margin-bottom: 20px;
      color: var(--primary-color);
      text-align: center;
    }

    .about-content {
      max-width: 800px;
      margin: 0 auto;
      text-align: center;
      font-size: 18px;
    }

    .pricing-cards {
      display: flex;
      justify-content: center;
      gap: 30px;
      margin-top: 40px;
    }

    .pricing-card {
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--shadow);
      overflow: hidden;
      width: 300px;
      transition: var(--transition);
      position: relative;
    }

    .pricing-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .card-header {
      padding: 20px;
      background-color: var(--primary-light);
      text-align: center;
    }

    .premium .card-header {
      background-color: var(--primary-color);
      color: white;
    }

    .card-header h3 {
      font-size: 24px;
      margin-bottom: 10px;
    }

    .price {
      font-size: 36px;
      font-weight: 700;
    }

    .price span {
      font-size: 16px;
      font-weight: 400;
    }

    .card-body {
      padding: 20px;
    }

    .features-list {
      list-style: none;
      margin-bottom: 20px;
    }

    .features-list li {
      padding: 8px 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .features-list i {
      color: var(--success-color);
    }

    .premium .btn {
      background-color: var(--primary-color);
      color: white;
    }

    .premium .btn:hover {
      background-color: var(--primary-hover);
    }

    .contact-info, .policies {
      background: white;
      padding: 30px;
      border-radius: var(--border-radius);
      box-shadow: var(--shadow);
      max-width: 800px;
      margin: 0 auto;
    }

    .contact-info p, .policies p {
      margin-bottom: 15px;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      padding: 30px 0;
      text-align: center;
    }

    .social-links {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-bottom: 20px;
    }

    .social-links a {
      color: white;
      font-size: 20px;
      transition: var(--transition);
    }

    .social-links a:hover {
      transform: translateY(-3px);
    }

    /* Step Cards */
    .usage-steps {
      display: flex;
      flex-direction: column;
      gap: 25px;
      margin-top: 40px;
    }

    .step-card {
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--shadow);
      overflow: hidden;
      transition: var(--transition);
    }

    .step-card:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .step-header {
      padding: 15px 20px;
      background-color: var(--primary-light);
      border-bottom: 1px solid var(--border-color);
    }

    .step-header h3 {
      display: flex;
      align-items: center;
      gap: 10px;
      color: var(--primary-color);
    }

    .step-header h3 i {
      background-color: var(--primary-color);
      color: white;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }

    .step-body {
      padding: 20px;
    }

    .step-body p {
      margin-bottom: 15px;
    }

    .step-body p:last-child {
      margin-bottom: 0;
    }

    .tip {
      background-color: var(--primary-light);
      padding: 10px 15px;
      border-radius: var(--border-radius);
      border-left: 4px solid var(--primary-color);
    }

    /* Features Grid */
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 30px;
      margin-top: 40px;
    }

    .feature-card {
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--shadow);
      padding: 25px;
      transition: var(--transition);
    }

    .feature-card:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .feature-icon {
      font-size: 28px;
      color: var(--primary-color);
      margin-bottom: 15px;
    }

    .feature-card h3 {
      margin-bottom: 10px;
      color: var(--text-color);
    }

    .shortcut-list {
      list-style: none;
      margin-top: 10px;
    }

    .shortcut-list li {
      margin-bottom: 5px;
    }

    kbd {
      background-color: #f7f7f7;
      border: 1px solid #ccc;
      border-radius: 3px;
      box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2);
      color: #333;
      display: inline-block;
      font-family: monospace;
      font-size: 0.85em;
      font-weight: 700;
      line-height: 1;
      padding: 2px 5px;
      white-space: nowrap;
    }

    /* Responsive Styles */
    @media (max-width: 768px) {
      .features-grid {
        grid-template-columns: 1fr;
      }

      .header-content {
        flex-direction: column;
        gap: 20px;
      }

      nav {
        flex-wrap: wrap;
        justify-content: center;
      }

      .hero h1 {
        font-size: 36px;
      }
    }
  </style>
</head>
<body>
  <header>
    <div class="container header-content">
      <div class="logo">
        <i class="fa-solid fa-comment-dots"></i>
        <span>ReplyPal</span>
      </div>
      <nav>
        <a href="#about">About</a>
        <a href="#how-to-use">How to Use</a>
        <a href="#features">Features</a>
        <a href="#contact">Contact</a>
        <a href="#policies">Policies</a>
      </nav>
    </div>
  </header>

  <section class="hero">
    <div class="container">
      <h1>Smart Email Replies with AI</h1>
      <p>Save time and enhance your communication with ReplyPal's AI-powered email assistant</p>
      <a href="#how-to-use" class="btn">Learn How to Use</a>
    </div>
  </section>

  <section id="about">
    <div class="container">
      <h2>About ReplyPal</h2>
      <div class="about-content">
        <p>
          ReplyPal is a Chrome extension that helps professionals craft smart, context-aware replies to emails and messages. Using cutting-edge AI, ReplyPal saves you time and enhances your communication. With just a few clicks, transform any selected text into a perfectly crafted response that matches your desired tone and purpose.
        </p>
      </div>
    </div>
  </section>

  <section id="how-to-use">
    <div class="container">
      <h2>How to Use ReplyPal</h2>
      <div class="usage-steps">
        <div class="step-card">
          <div class="step-header">
            <h3><i class="fa-solid fa-1"></i> Select Text</h3>
          </div>
          <div class="step-body">
            <p>Highlight any text on a webpage that you want to respond to. This could be an email, message, comment, or any text.</p>
            <p class="tip"><i class="fa-solid fa-lightbulb"></i> <strong>Tip:</strong> The more context you provide in your selection, the better the AI response will be.</p>
          </div>
        </div>

        <div class="step-card">
          <div class="step-header">
            <h3><i class="fa-solid fa-2"></i> Open ReplyPal</h3>
          </div>
          <div class="step-body">
            <p>Click the ReplyPal icon that appears near your selection, or use the keyboard shortcut <kbd>Ctrl</kbd>+<kbd>Alt</kbd>+<kbd>R</kbd>.</p>
            <p class="tip"><i class="fa-solid fa-lightbulb"></i> <strong>Tip:</strong> You can also right-click and select "ReplyPal" from the context menu.</p>
          </div>
        </div>

        <div class="step-card">
          <div class="step-header">
            <h3><i class="fa-solid fa-3"></i> Customize Your Response</h3>
          </div>
          <div class="step-body">
            <ul class="features-list">
              <li><i class="fa-solid fa-pen"></i> <strong>Intent:</strong> Describe what you want to say (optional)</li>
              <li><i class="fa-solid fa-palette"></i> <strong>Tone:</strong> Choose from friendly, formal, casual, empathetic, professional, neutral, or emoji</li>
              <li><i class="fa-solid fa-bullseye"></i> <strong>Purpose:</strong> Select reply, explain, analyze, summarize, ask, suggest, apologize, or thank</li>
            </ul>
          </div>
        </div>

        <div class="step-card">
          <div class="step-header">
            <h3><i class="fa-solid fa-4"></i> Generate & Use</h3>
          </div>
          <div class="step-body">
            <p>Click "Generate" or press <kbd>Ctrl</kbd>+<kbd>Enter</kbd> to create your response. Once generated, you can:</p>
            <ul class="features-list">
              <li><i class="fa-solid fa-edit"></i> <strong>Edit:</strong> Modify the response to your liking</li>
              <li><i class="fa-solid fa-sync"></i> <strong>Regenerate:</strong> Create a new version if needed</li>
              <li><i class="fa-solid fa-copy"></i> <strong>Copy:</strong> Copy the response to your clipboard</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section id="features">
    <div class="container">
      <h2>Key Features</h2>
      <div class="features-grid">
        <div class="feature-card">
          <i class="fa-solid fa-bolt feature-icon"></i>
          <h3>Quick Access</h3>
          <p>Access ReplyPal instantly with the keyboard shortcut <kbd>Ctrl</kbd>+<kbd>Alt</kbd>+<kbd>R</kbd> or by clicking the icon that appears near selected text.</p>
        </div>

        <div class="feature-card">
          <i class="fa-solid fa-stream feature-icon"></i>
          <h3>Real-time Streaming</h3>
          <p>Watch responses appear in real-time as they're generated, with no waiting for the complete response.</p>
        </div>

        <div class="feature-card">
          <i class="fa-solid fa-history feature-icon"></i>
          <h3>Response History</h3>
          <p>Access your last 10 responses in the History tab, making it easy to reuse or reference previous replies.</p>
        </div>

        <div class="feature-card">
          <i class="fa-solid fa-keyboard feature-icon"></i>
          <h3>Keyboard Shortcuts</h3>
          <p>Work efficiently with shortcuts for all major actions:</p>
          <ul class="shortcut-list">
            <li><kbd>Ctrl</kbd>+<kbd>Alt</kbd>+<kbd>R</kbd>: Open ReplyPal</li>
            <li><kbd>Ctrl</kbd>+<kbd>Enter</kbd>: Generate Response</li>
            <li><kbd>Esc</kbd>: Close ReplyPal</li>
            <li><kbd>Ctrl</kbd>+<kbd>E</kbd>: Edit Response</li>
            <li><kbd>Ctrl</kbd>+<kbd>R</kbd>: Regenerate Response</li>
            <li><kbd>Ctrl</kbd>+<kbd>C</kbd>: Copy Response</li>
          </ul>
        </div>
      </div>
    </div>
  </section>

  <section id="contact">
    <div class="container">
      <h2>Contact Us</h2>
      <div class="contact-info">
        <p><i class="fa-solid fa-envelope"></i> Email: <EMAIL></p>

      </div>
    </div>
  </section>

  <section id="policies">
    <div class="container">
      <h2>Policies</h2>
      <div class="policies">
        
        <p><strong>Terms of Service:</strong> By using ReplyPal, you agree to our terms outlined on our <a href="terms.html" style="color: var(--primary-color);">Terms page</a>.</p>
      </div>
    </div>
  </section>

  <footer>
    <div class="container">

      <p>&copy; 2024 ReplyPal. All rights reserved.</p>
    </div>
  </footer>
</body>
</html>
