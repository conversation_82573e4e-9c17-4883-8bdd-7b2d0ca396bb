<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/animations.css">
  <link rel="stylesheet" href="css/icons.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <div class="container">
    <header class="header-bar">
      <div class="header-title">
        <i class="fa-solid fa-comment-dots"></i>
        <h1>ReplyPal</h1>
      </div>
      <div class="header-icons">
        <button class="tab-icon-btn active" data-tab="compose" title="Compose">
          <i class="fa-solid fa-pen-to-square"></i>
        </button>
        <button class="tab-icon-btn" data-tab="history" title="History">
          <i class="fa-solid fa-clock-rotate-left"></i>
        </button>
        <button class="tab-icon-btn" data-tab="settings" title="Settings">
          <i class="fa-solid fa-gear"></i>
        </button>
        <button id="upgrade-btn" class="upgrade-btn" title="Upgrade to Basic Plan" style="display: none;">
          <i class="fa-solid fa-arrow-up"></i>
        </button>
        <button class="header-close-btn" title="Close">
          <i class="fa-solid fa-xmark"></i>
        </button>
      </div>
    </header>

    <main>
      <!-- Compose Tab -->
      <section id="compose" class="tab-content active">
        <!-- Selected Text Section (hidden when no text is selected) -->
        <div id="selected-text-container" class="form-group">
          <label for="selected-text">
            Selected Text
            <span class="info-icon" title="The text you've selected on the page.">
              <i class="fa-solid fa-circle-info"></i>
            </span>
          </label>
          <textarea id="selected-text" placeholder="No text selected. You can type or paste text here."></textarea>
        </div>

        <!-- User Intent Section -->
        <div class="form-group">
          <label for="user-intent">
            Your Intent
            <span class="info-icon" title="Tell the AI what you want to do with the text. E.g., 'Reply politely to this email' or 'Write a thank you note' or leave it blank to let the AI to respond based on the selected text.">
              <i class="fa-solid fa-circle-info"></i>
            </span>
          </label>
          <textarea id="user-intent" placeholder="What do you want to say? E.g., 'Reply politely to this email'" required></textarea>
        </div>

        <!-- Controls Row -->
        <div class="controls-row">
          <div class="control-group">
            <label for="tone" class="control-label">
              Tone
              <span class="info-icon" title="Choose the tone for your response">
                <i class="fa-solid fa-circle-info"></i>
              </span>
            </label>
            <select id="tone" class="control-select">
              <option value="friendly">Friendly</option>
              <option value="emoji">Emoji</option>
              <option value="formal">Formal</option>
              <option value="casual">Casual</option>
              <option value="empathetic">Empathetic</option>
              <option value="professional">Professional</option>
              <option value="neutral">Neutral</option>
            </select>
          </div>

          <div id="purpose-container" class="control-group">
            <label for="purpose" class="control-label">
              Purpose
              <span class="info-icon" title="Select what you're trying to do">
                <i class="fa-solid fa-circle-info"></i>
              </span>
            </label>
            <select id="purpose" class="control-select">
              <option value="reply">Reply</option>
              <option value="rewrite">Rewrite</option>
              <option value="summarize">Summarize</option>
              <option value="explain">Explain</option>
              <option value="thank">Thank</option>
              <option value="apologize">Apologize</option>
              <option value="suggest">Suggest</option>
              <option value="ask">Ask</option>
            </select>
          </div>

          <div class="generate-container">
            <button id="generate-btn" class="primary-btn">
              <span class="generate-btn-spinner"></span>
              <i class="fa-solid fa-wand-magic-sparkles"></i> Generate
            </button>
            <!-- Usage indicators will be inserted here dynamically -->
          </div>
        </div>

        <!-- Response Section (hidden initially) -->
        <div id="response-container" class="response-container hidden">
          <div class="response-header">

            <div class="action-buttons">
              <button id="edit-btn" class="action-btn" title="Edit Response">
                <i class="fa-solid fa-pen-to-square"></i> Edit
              </button>
              <button id="regenerate-btn" class="action-btn" title="Regenerate Response">
                <i class="fa-solid fa-arrows-rotate"></i> Regenerate
              </button>
              <button id="copy-btn" class="action-btn" title="Copy to Clipboard">
                <i class="fa-regular fa-copy"></i> Copy
              </button>
            </div>
          </div>
          <div class="response-content">
            <div id="response-text" class="response-box"></div>
          </div>
        </div>

        <!-- Loading Spinner -->
        <div id="loading-spinner" class="loading-spinner">
          <div class="spinner"></div>
        </div>
      </section>

      <!-- History Tab -->
      <section id="history" class="tab-content">
        <div class="history-container">
          <div class="history-header">
            <h3>Your Response History</h3>
            <button id="clear-history-btn" class="secondary-btn">
              <i class="fa-solid fa-trash-can"></i> Clear History
            </button>
          </div>
          <div id="history-list" class="history-list">
            <p class="empty-message">Your history will appear here.</p>
          </div>
        </div>
      </section>

      <!-- Settings Tab -->
      <section id="settings" class="tab-content">
        <div class="settings-container">
          <h3>Account</h3>
          <div class="form-group" id="auth-container">
            <div class="auth-section">
              <button id="loginBtn" class="secondary-btn">
                <i class="fab fa-google"></i> Login with Google
              </button>
              <div id="user-info" style="display: none;">
                <div class="user-info-display">
                  <span id="user-name"></span>
                  <button id="logoutBtn" class="secondary-btn">
                    <i class="fa-solid fa-sign-out-alt"></i> Logout
                  </button>
                </div>
              </div>
            </div>
            <p class="setting-info">Login to save your settings across devices.</p>
          </div>

          <div class="form-group" id="usage-container">
            <h3>Usage</h3>
            <div class="usage-info">
              <div class="usage-meter">
                <div id="usage-progress" class="usage-progress"></div>
              </div>
              <p id="usage-text">Remaining responses: <span id="remaining-responses">-</span> of <span id="total-responses">5</span> today</p>
            </div>
            <p class="setting-info">Free tier includes 5 AI responses per day. <a href="#" id="upgrade-link">Upgrade</a> for more.</p>
          </div>

          <h3>API Settings</h3>
          <div class="form-group">
            <label for="environment">Environment</label>
            <select id="environment" class="form-control">
              <option value="local">Local Development</option>
              <option value="dev">Development Server</option>
              <option value="prod">Production</option>
            </select>
            <p class="setting-info">Current API URL: <span id="current-api-url">http://localhost:8000</span></p>
            <button id="test-api-btn" class="secondary-btn">Test Connection</button>
            <p id="api-test-result" class="setting-result"></p>
          </div>

          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" id="save-history" checked>
              <span>Save Response History</span>
            </label>
            <p class="setting-info">When enabled, your last 10 responses will be saved locally and can be reused.</p>
          </div>

          <div class="form-group" style="display: none;">
            <label class="checkbox-label">
              <input type="checkbox" id="use-mock-api">
              <span>Use Mock API (for testing)</span>
            </label>
          </div>

          <div class="hotkeys-info">
            <div class="hotkeys-header">
              <h3>Keyboard Shortcuts</h3>
              <button id="toggle-hotkeys-btn" class="toggle-btn" title="Show all shortcuts">
                <i class="fa-solid fa-chevron-down"></i>
              </button>
            </div>
            <!-- Primary shortcut (always visible) -->
            <div class="hotkey primary-hotkey">
              <span class="key-combo"><kbd>Ctrl</kbd> + <kbd>Alt</kbd> + <kbd>R</kbd></span>
              <span class="key-desc">Open ReplyPal</span>
            </div>
            <!-- Additional shortcuts (hidden by default) -->
            <div id="additional-hotkeys" class="additional-hotkeys hidden">
              <div class="hotkey">
                <span class="key-combo"><kbd>Ctrl</kbd> + <kbd>Enter</kbd></span>
                <span class="key-desc">Generate Response</span>
              </div>
              <div class="hotkey">
                <span class="key-combo"><kbd>Esc</kbd></span>
                <span class="key-desc">Close ReplyPal</span>
              </div>
              <div class="hotkey">
                <span class="key-combo"><kbd>Ctrl</kbd> + <kbd>E</kbd></span>
                <span class="key-desc">Edit Response</span>
              </div>
              <div class="hotkey">
                <span class="key-combo"><kbd>Ctrl</kbd> + <kbd>R</kbd></span>
                <span class="key-desc">Regenerate Response</span>
              </div>
              <div class="hotkey">
                <span class="key-combo"><kbd>Ctrl</kbd> + <kbd>C</kbd></span>
                <span class="key-desc">Copy Response (when focused)</span>
              </div>
            </div>
          </div>

          <button id="save-settings-btn" class="primary-btn">Save Settings</button>
        </div>
      </section>
    </main>

    <!-- Toast Notification -->
    <div id="toast" class="toast"></div>
  </div>

  <!-- App Scripts -->
  <script src="js/settings.js"></script>
  <script src="js/main.js"></script>
  <script src="js/history.js"></script>
  <script src="js/ui.js"></script>

  <!-- Authentication -->
  <script type="module" src="js/login_popup.js"></script>

  <!-- Check login status -->
  <script src="js/auth_check.js"></script>
</body>
</html>
