/**
 * ReplyPal - Content Script
 * Handles text selection, floating icon, and popup management
 */

// Check if the script has already been loaded
if (typeof window.replyPalVariablesInitialized === 'undefined') {
  // Set a flag to indicate variables are initialized
  window.replyPalVariablesInitialized = true;

  // Global state variables
  window.floatingIcon = null;
  window.tooltip = null;
  window.floatingPopup = null;
  window.popupIframe = null;
  window.lastSelectedText = '';
  window.isDragging = false;
  window.dragOffsetX = 0;
  window.dragOffsetY = 0;
  window.lastSelectionRect = null;
  window.messageListener = null;
}

// We'll directly use the window variables instead of creating local copies
// This ensures we're always working with the most up-to-date values

/**
 * Debounce function to limit how often a function is called
 */
function debounce(func, wait) {
  let timeout;
  return function(...args) {
    const context = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(context, args), wait);
  };
}

/**
 * Handle text selection
 */
// Only define handleTextSelection if it doesn't already exist in the window object
if (typeof window.handleTextSelection === 'undefined') {
  window.handleTextSelection = debounce(() => {
    const selection = window.getSelection();
    const selectedText = selection.toString().trim();

    console.log('Text selection detected:', selectedText ? `"${selectedText.substring(0, 30)}${selectedText.length > 30 ? '...' : ''}"` : 'No text selected');

    if (selectedText && selectedText.length > 0) {
      // Store the selected text
      window.lastSelectedText = selectedText;

      // Store the selection position
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);

        // Get all client rects for the selection (handles multi-line selections better)
        const rects = range.getClientRects();

        if (rects.length > 0) {
          // Use the bounding rect for overall dimensions
          const boundingRect = range.getBoundingClientRect();

          // Store the bounding rect
          window.lastSelectionRect = {
            left: boundingRect.left + window.scrollX,
            right: boundingRect.right + window.scrollX,
            top: boundingRect.top + window.scrollY,
            bottom: boundingRect.bottom + window.scrollY,
            width: boundingRect.width,
            height: boundingRect.height
          };

          // Also store the last rect which represents the end of the selection
          const lastRect = rects[rects.length - 1];
          window.lastSelectionEndRect = {
            left: lastRect.left + window.scrollX,
            right: lastRect.right + window.scrollX,
            top: lastRect.top + window.scrollY,
            bottom: lastRect.bottom + window.scrollY,
            width: lastRect.width,
            height: lastRect.height
          };

          console.log('Selection rect:', window.lastSelectionRect);
          console.log('Selection end rect:', window.lastSelectionEndRect);
        }
      }

      // Show the floating icon near the selection
      console.log('Calling showFloatingIcon()');
      showFloatingIcon();
    } else {
      // Hide the floating icon if no text is selected
      console.log('Calling hideFloatingIcon()');
      hideFloatingIcon();
    }
  }, 300);
}

/**
 * Show the floating icon near the selection
 */
function showFloatingIcon() {
  console.log('showFloatingIcon called, floatingIcon:', window.floatingIcon ? 'exists' : 'null',
              'lastSelectionRect:', window.lastSelectionRect ? 'exists' : 'null');

  if (!window.floatingIcon || !window.lastSelectionRect) {
    console.log('Cannot show floating icon: floatingIcon or lastSelectionRect is missing');
    return;
  }

  // Position the icon near the end of the selection
  const iconSize = 36; // Size of the icon
  const spacing = 5; // Reduced spacing for closer positioning to text

  // Get the selection object to determine the exact end position
  const selection = window.getSelection();
  let left, top;

  if (selection && selection.rangeCount > 0) {
    // Get the client rectangle of the selection
    const range = selection.getRangeAt(0);
    const rects = range.getClientRects();

    if (rects.length > 0) {
      // Use the last rect which represents the end of the selection
      const lastRect = rects[rects.length - 1];

      // Position at the end of the last line of selection
      left = lastRect.right + window.scrollX + spacing;
      top = lastRect.top + window.scrollY + (lastRect.height / 2) - (iconSize / 2);

      console.log('Positioning based on selection end point:', { left, top });
    } else {
      // Fallback to the bounding rect if client rects aren't available
      left = window.lastSelectionRect.right + spacing;
      top = window.lastSelectionRect.top + (window.lastSelectionRect.height / 2) - (iconSize / 2);
      console.log('Fallback positioning based on bounding rect:', { left, top });
    }
  } else {
    // Fallback if selection object isn't available
    left = window.lastSelectionRect.right + spacing;
    top = window.lastSelectionRect.top + (window.lastSelectionRect.height / 2) - (iconSize / 2);
    console.log('Fallback positioning based on stored rect:', { left, top });
  }

  // Ensure the icon is within the viewport
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  const scrollX = window.scrollX;
  const scrollY = window.scrollY;

  // Check right boundary
  if (left + iconSize > scrollX + viewportWidth - spacing) {
    // If it would go off-screen to the right, try positioning to the left of the selection
    if (window.lastSelectionRect.left - iconSize - spacing > scrollX) {
      left = window.lastSelectionRect.left - iconSize - spacing;
      console.log('Repositioning to left of selection:', { left });
    } else {
      // If that doesn't work either, just keep it within the right viewport edge
      left = scrollX + viewportWidth - iconSize - spacing;
      console.log('Keeping within right viewport edge:', { left });
    }
  }

  // Check left boundary
  if (left < scrollX + spacing) {
    left = scrollX + spacing;
    console.log('Keeping within left viewport edge:', { left });
  }

  // Check top boundary
  if (top < scrollY + spacing) {
    top = scrollY + spacing;
    console.log('Keeping within top viewport edge:', { top });
  }
  // Check bottom boundary
  else if (top + iconSize > scrollY + viewportHeight - spacing) {
    top = scrollY + viewportHeight - iconSize - spacing;
    console.log('Keeping within bottom viewport edge:', { top });
  }

  console.log('Final icon position:', { left, top });

  // Set position
  window.floatingIcon.style.left = `${left}px`;
  window.floatingIcon.style.top = `${top}px`;
  window.floatingIcon.style.display = 'flex';
  window.floatingIcon.style.zIndex = '2147483646'; // Ensure high z-index

  console.log('Floating icon should now be visible');
}

/**
 * Hide the floating icon
 */
function hideFloatingIcon() {
  console.log('hideFloatingIcon called, floatingIcon:', window.floatingIcon ? 'exists' : 'null');

  if (window.floatingIcon) {
    window.floatingIcon.style.display = 'none';
    console.log('Floating icon hidden');
  } else {
    console.log('Cannot hide floating icon: floatingIcon is missing');
  }
}

/**
 * Show the tooltip
 */
function showTooltip() {
  if (!window.tooltip || !window.floatingIcon) return;

  // Position the tooltip near the icon
  const iconRect = window.floatingIcon.getBoundingClientRect();
  const tooltipHeight = 32; // Adjusted height for the smaller tooltip

  window.tooltip.style.left = `${iconRect.left + iconRect.width / 2}px`;
  window.tooltip.style.top = `${iconRect.top - tooltipHeight - 6}px`; // Adjusted spacing for smaller icon
  window.tooltip.style.transform = 'translateX(-50%)';
  window.tooltip.style.display = 'block';

  // Make the tooltip visible
  setTimeout(() => {
    window.tooltip.classList.add('visible');
  }, 10);
}

/**
 * Hide the tooltip
 */
function hideTooltip() {
  if (!window.tooltip) return;

  window.tooltip.classList.remove('visible');

  // Hide the tooltip after the transition
  setTimeout(() => {
    window.tooltip.style.display = 'none';
  }, 200);
}

/**
 * Handle icon click
 */
function handleIconClick() {
  // Store the selected text
  chrome.runtime.sendMessage({
    action: 'storeSelectedText',
    text: window.lastSelectedText
  });

  // Create and show the floating popup
  createFloatingPopup();
}

/**
 * Close the floating popup
 */
function closeFloatingPopup() {
  if (window.floatingPopup) {
    // Remove event listeners
    if (window.messageListener) {
      window.removeEventListener('message', window.messageListener);
      window.messageListener = null;
    }

    // Remove the popup
    window.floatingPopup.remove();
    window.floatingPopup = null;
    window.popupIframe = null;
  }
}

/**
 * Start dragging the popup
 */
function startDragging(e) {
  if (!window.floatingPopup) return;

  // Prevent default behavior
  e.preventDefault();

  // Get initial position
  const popupRect = window.floatingPopup.getBoundingClientRect();

  // Calculate offset
  window.dragOffsetX = e.clientX - popupRect.left;
  window.dragOffsetY = e.clientY - popupRect.top;

  // Set dragging flag
  window.isDragging = true;

  // Add event listeners
  document.addEventListener('mousemove', dragPopup);
  document.addEventListener('mouseup', stopDragging);
}

/**
 * Drag the popup
 */
function dragPopup(e) {
  if (!window.isDragging || !window.floatingPopup) return;

  // Calculate new position
  const newLeft = e.clientX - window.dragOffsetX;
  const newTop = e.clientY - window.dragOffsetY;

  // Set new position
  window.floatingPopup.style.left = `${newLeft}px`;
  window.floatingPopup.style.top = `${newTop}px`;
}

/**
 * Stop dragging the popup
 */
function stopDragging() {
  window.isDragging = false;

  // Remove event listeners
  document.removeEventListener('mousemove', dragPopup);
  document.removeEventListener('mouseup', stopDragging);
}

/**
 * Create floating elements
 */
function createFloatingElements() {
  console.log('Creating floating elements');
  try {
    // Reset global variables
    window.floatingIcon = null;
    window.tooltip = null;

    // Remove any existing elements
    const existingIcon = document.querySelector('.replypal-icon');
    if (existingIcon) {
      console.log('Removing existing icon');
      existingIcon.remove();
    }

    const existingTooltip = document.querySelector('.replypal-tooltip');
    if (existingTooltip) {
      console.log('Removing existing tooltip');
      existingTooltip.remove();
    }

    // Create floating icon
    const newFloatingIcon = document.createElement('div');
    newFloatingIcon.className = 'replypal-icon';
    // Use speech bubble icon + text design to match the image
    newFloatingIcon.innerHTML = `
      <svg class="replypal-icon-bubble" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 13.8214 2.48697 15.5291 3.33782 17L2.5 21.5L7 20.6622C8.47087 21.513 10.1786 22 12 22Z"
          fill="white" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <span class="replypal-icon-text">ReplyPal</span>
    `;
    newFloatingIcon.style.display = 'none';

    console.log('Created new floating icon element');

    // Add our custom icons CSS if not already loaded
    if (!document.querySelector('link[href*="icons.css"]')) {
      console.log('Adding icons.css stylesheet');
      const iconsStyle = document.createElement('link');
      iconsStyle.rel = 'stylesheet';
      iconsStyle.href = chrome.runtime.getURL('css/icons.css');
      document.head.appendChild(iconsStyle);
    }

    // Add event listeners
    newFloatingIcon.addEventListener('click', (e) => {
      console.log('Floating icon clicked');
      e.stopPropagation();
      handleIconClick();
    });

    newFloatingIcon.addEventListener('mouseenter', showTooltip);
    newFloatingIcon.addEventListener('mouseleave', hideTooltip);

    // Add to document body
    document.body.appendChild(newFloatingIcon);
    console.log('Added floating icon to document body');

    // Update global variable
    window.floatingIcon = newFloatingIcon;
    console.log('Updated global floatingIcon variable');

    // Create tooltip
    const newTooltip = document.createElement('div');
    newTooltip.className = 'replypal-tooltip';
    newTooltip.textContent = 'Generate with ReplyPal';
    newTooltip.style.display = 'none';
    document.body.appendChild(newTooltip);
    console.log('Created and added tooltip to document body');

    // Update global variable
    window.tooltip = newTooltip;
    console.log('Updated global tooltip variable');

    // Verify the elements were created successfully
    console.log('Floating elements created successfully:', {
      floatingIconExists: !!window.floatingIcon,
      tooltipExists: !!window.tooltip
    });
  } catch (error) {
    console.error('Error creating floating elements:', error);
    window.floatingIcon = null;
    window.tooltip = null;
  }
}

/**
 * Create floating popup
 */
function createFloatingPopup() {
  // Close any existing popup
  closeFloatingPopup();

  // Remove any existing popup
  const existingPopup = document.querySelector('.replypal-floating-popup');
  if (existingPopup) {
    existingPopup.remove();
  }

  // Create popup container
  const newFloatingPopup = document.createElement('div');
  newFloatingPopup.className = 'replypal-floating-popup';

  // Update global variable
  window.floatingPopup = newFloatingPopup;

  // Determine position
  const viewportWidth = window.innerWidth;
  const popupWidth = 400; // Use a more flexible default
  const fixedTop = 50;

  // Calculate horizontal position
  let left;
  if (window.lastSelectionRect) {
    if (window.lastSelectionRect.right + popupWidth + 20 <= viewportWidth) {
      left = window.lastSelectionRect.right + 20;
    } else if (window.lastSelectionRect.left - popupWidth - 20 >= 0) {
      left = window.lastSelectionRect.left - popupWidth - 20;
    } else {
      left = Math.max(10, (viewportWidth - popupWidth) / 2);
    }
  } else {
    left = Math.max(10, (viewportWidth - popupWidth) / 2);
  }

  // Set position (no fixed width/height)
  window.floatingPopup.style.top = `${fixedTop}px`;
  window.floatingPopup.style.left = `${left}px`;
  window.floatingPopup.style.right = 'auto';
  window.floatingPopup.style.minWidth = '400px';
  window.floatingPopup.style.minHeight = '300px';
  window.floatingPopup.style.maxWidth = '90vw';
  window.floatingPopup.style.maxHeight = '90vh';
  window.floatingPopup.style.width = '480px'; // Fixed width
  window.floatingPopup.style.height = '500px'; // Fixed height

  // Create a draggable area at the top of the popup
  const dragHandle = document.createElement('div');
  dragHandle.className = 'replypal-popup-drag-handle';
  dragHandle.style.position = 'absolute';
  dragHandle.style.top = '0';
  dragHandle.style.left = '0';
  dragHandle.style.right = '0';
  dragHandle.style.height = '10px';
  dragHandle.style.cursor = 'move';
  dragHandle.style.zIndex = '1';
  window.floatingPopup.appendChild(dragHandle);

  // Make the drag handle draggable
  dragHandle.addEventListener('mousedown', startDragging);

  // Create content container
  const content = document.createElement('div');
  content.className = 'replypal-popup-content';

  // Create iframe
  const newPopupIframe = document.createElement('iframe');
  newPopupIframe.className = 'replypal-popup-iframe';

  // Check if user is logged in
  chrome.storage.local.get('user', (data) => {
    if (data.user) {
      // User is logged in, show main interface
      newPopupIframe.src = chrome.runtime.getURL('index.html');
    } else {
      // User is not logged in, show login page
      newPopupIframe.src = chrome.runtime.getURL('login.html');
    }
  });

  newPopupIframe.style.width = '100%';
  newPopupIframe.style.height = '100%';
  newPopupIframe.style.minWidth = '200px';
  newPopupIframe.style.minHeight = '100px';
  newPopupIframe.style.border = 'none';
  newPopupIframe.style.overflow = 'auto';
  newPopupIframe.style.resize = 'none';
  newPopupIframe.style.display = 'block';

  // Update global variable
  window.popupIframe = newPopupIframe;

  // Add load event listener
  window.popupIframe.addEventListener('load', () => {
    // No fixed height, let it fill parent
    window.popupIframe.style.height = '100%';
  });

  content.appendChild(window.popupIframe);
  window.floatingPopup.appendChild(content);

  // Add our custom icons CSS if not already loaded
  if (!document.querySelector('link[href*="icons.css"]')) {
    const iconsStyle = document.createElement('link');
    iconsStyle.rel = 'stylesheet';
    iconsStyle.href = chrome.runtime.getURL('css/icons.css');
    document.head.appendChild(iconsStyle);
  }

  // Add CSS for floating popup
  const popupStyle = document.createElement('link');
  popupStyle.rel = 'stylesheet';
  popupStyle.href = chrome.runtime.getURL('css/floating-popup.css');
  document.head.appendChild(popupStyle);

  // Set up message listener
  if (window.messageListener) {
    window.removeEventListener('message', window.messageListener);
  }

  const newMessageListener = (event) => {
    // Only accept messages from our iframe
    if (!window.popupIframe || event.source !== window.popupIframe.contentWindow) {
      return;
    }

    const message = event.data;

    // Handle generate request
    if (message.action === 'generateResponse' || message.action === 'generateStreamingResponse') {
      // Forward to background script
      chrome.runtime.sendMessage({
        action: message.action,
        data: message.data
      }, (response) => {
        // Forward response to iframe
        if (window.popupIframe && window.popupIframe.contentWindow) {
          window.popupIframe.contentWindow.postMessage({
            action: message.action === 'generateResponse' ? 'generateResponseResult' : 'streamResponseAck',
            success: response?.success || false,
            data: response?.data,
            error: response?.error
          }, '*');
        }
      });
    }

    // Handle clipboard request
    else if (message.action === 'copyToClipboard') {
      // Forward to background script
      chrome.runtime.sendMessage({
        action: 'copyToClipboard',
        text: message.text
      }, (response) => {
        // Forward result to iframe
        if (window.popupIframe && window.popupIframe.contentWindow) {
          window.popupIframe.contentWindow.postMessage({
            action: 'copyToClipboardResult',
            success: response?.success || false,
            error: response?.error
          }, '*');
        }
      });
    }

    // Handle height adjustment
    else if (message.action === 'adjustHeight') {
      if (window.popupIframe) {
        const contentHeight = message.height || 400;
        window.popupIframe.style.height = `${contentHeight + 20}px`;
      }
    }

    // Handle close popup request
    else if (message.action === 'closePopup') {
      closeFloatingPopup();
    }

    // Handle scroll to response
    else if (message.action === 'scrollToResponse') {
      // Ensure the popup is visible
      if (window.floatingPopup) {
        const viewportHeight = window.innerHeight;
        const popupRect = window.floatingPopup.getBoundingClientRect();
        const responseTop = message.responseTop || 0;

        // If the response would be below the viewport, adjust the popup position
        if (popupRect.top + responseTop + 200 > viewportHeight) {
          const newTop = Math.max(50, viewportHeight - responseTop - 250);
          window.floatingPopup.style.top = `${newTop}px`;
        }
      }
    }
  };

  // Update global variable
  window.messageListener = newMessageListener;

  window.addEventListener('message', window.messageListener);

  // Add to document
  document.body.appendChild(window.floatingPopup);

  // Show the popup
  setTimeout(() => {
    window.floatingPopup.classList.add('visible');
  }, 10);

  // Hide the floating icon
  hideFloatingIcon();
}

/**
 * Handle messages from the extension
 */
function handleMessages(message, _sender, sendResponse) {
  if (message.action === 'closePopup') {
    closeFloatingPopup();
    sendResponse({ success: true });
  } else if (message.action === 'streamResponseChunk') {
    // Forward streaming response chunk to the iframe
    if (window.popupIframe && window.popupIframe.contentWindow) {
      window.popupIframe.contentWindow.postMessage({
        action: 'streamResponseChunk',
        chunk: message.chunk
      }, '*');
    }
    sendResponse({ success: true });
  } else if (message.action === 'streamResponseError') {
    // Forward streaming error to the iframe
    if (window.popupIframe && window.popupIframe.contentWindow) {
      window.popupIframe.contentWindow.postMessage({
        action: 'streamResponseError',
        error: message.error
      }, '*');
    }
    sendResponse({ success: true });
  } else if (message.action === 'showPopup') {
    // Store the selected text if provided
    if (message.text) {
      window.lastSelectedText = message.text;

      chrome.runtime.sendMessage({
        action: 'storeSelectedText',
        text: message.text
      });

      // If we don't have a selection position yet, try to get it from the current selection
      if (!window.lastSelectionRect) {
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          const rect = range.getBoundingClientRect();

          window.lastSelectionRect = {
            left: rect.left + window.scrollX,
            right: rect.right + window.scrollX,
            top: rect.top + window.scrollY,
            bottom: rect.bottom + window.scrollY,
            width: rect.width,
            height: rect.height
          };
        } else {
          // Default position if no selection is available
          window.lastSelectionRect = {
            left: window.innerWidth / 2 - 225,
            right: window.innerWidth / 2 + 225,
            top: window.innerHeight / 3,
            bottom: window.innerHeight / 3 + 100,
            width: 450,
            height: 100
          };
        }
      }
    }

    // Create and show the floating popup
    if (!window.floatingPopup) {
      createFloatingPopup();
    } else {
      // Make sure the popup is visible
      window.floatingPopup.style.display = 'flex';
      window.floatingPopup.classList.add('visible');

      // Check if the popup is in the DOM
      if (!document.body.contains(window.floatingPopup)) {
        window.floatingPopup = null;
        createFloatingPopup();
      }
    }

    sendResponse({ success: true });
  }
  return true;
}

/**
 * Handle keyboard shortcuts
 */
function handleKeyboardShortcuts(e) {
  // Check if ReplyPal popup is already open
  const popupExists = !!window.floatingPopup;

  // Handle Ctrl+Alt+R to open ReplyPal
  if (e.ctrlKey && e.altKey && e.key === 'r') {
    e.preventDefault();
    console.log('Keyboard shortcut detected: Ctrl+Alt+R');

    // If popup is already open, don't open another one
    if (popupExists) {
      console.log('Popup already exists, bringing to front');
      if (window.floatingPopup) {
        // Bring to front by adjusting z-index
        window.floatingPopup.style.zIndex = '2147483647';
      }
      return;
    }

    // Store any selected text
    const selection = window.getSelection();
    const selectedText = selection ? selection.toString().trim() : '';
    if (selectedText) {
      window.lastSelectedText = selectedText;
      chrome.runtime.sendMessage({
        action: 'storeSelectedText',
        text: selectedText
      });
    }

    // Create and show the popup
    createFloatingPopup();
  }

  // If popup is open, handle Escape key to close it
  if (popupExists && e.key === 'Escape') {
    console.log('Keyboard shortcut detected: Escape');
    closeFloatingPopup();
  }
}

/**
 * Initialize the content script
 */
function initialize() {
  console.log('Initializing ReplyPal content script');
  try {
    // Listen for text selection
    console.log('Adding text selection event listeners');
    document.addEventListener('mouseup', window.handleTextSelection);
    document.addEventListener('keyup', window.handleTextSelection);

    // Add keyboard shortcut listener
    console.log('Adding keyboard shortcut listener');
    document.addEventListener('keydown', handleKeyboardShortcuts);

    // Create floating elements
    console.log('Creating floating elements');
    createFloatingElements();

    // Test if the floating icon was created successfully
    console.log('Initialization complete, floating icon status:', window.floatingIcon ? 'created' : 'not created');

    // Notify the background script that the content script is ready
    console.log('Notifying background script that content script is ready');
    chrome.runtime.sendMessage({ action: 'contentScriptReady' })
      .catch(err => console.log('Error notifying background script:', err));

    // Force a check for any existing text selection
    console.log('Checking for existing text selection');
    setTimeout(() => {
      if (window.handleTextSelection) {
        window.handleTextSelection();
      }
    }, 500);
  } catch (error) {
    console.error('Error during initialization:', error);
  }
}

// Check if the content script is already initialized
if (!window.replyPalInitialized) {
  // Set a global flag to indicate the content script is loaded
  window.replyPalInitialized = true;

  // Initialize immediately
  initialize();

  // Add a fallback check to ensure the floating elements are created
  // This helps in case the initial initialization fails for any reason
  setTimeout(() => {
    if (!window.floatingIcon) {
      console.log('Fallback: Recreating floating elements');
      createFloatingElements();

      // Force a check for any existing text selection
      setTimeout(() => {
        if (window.handleTextSelection) {
          window.handleTextSelection();
        }
      }, 300);
    }
  }, 1000);
}

// Listen for messages from the extension
// Only add the listener if it hasn't been added before
if (!window.replyPalMessageListenerAdded) {
  window.replyPalMessageListenerAdded = true;
  chrome.runtime.onMessage.addListener(handleMessages);
}
