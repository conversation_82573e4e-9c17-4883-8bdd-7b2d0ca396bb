<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>ReplyPal - Terms of Service</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    :root {
      --primary-color: #4a6cf7;
      --primary-hover: #3a5ce5;
      --primary-light: #eef1ff;
      --secondary-color: #f5f7ff;
      --text-color: #333;
      --light-text: #666;
      --label-text: #444;
      --border-color: #e0e0e0;
      --success-color: #4caf50;
      --error-color: #f44336;
      --warning-color: #ff9800;
      --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      --border-radius: 8px;
      --transition: all 0.2s ease;
      --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
        Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: var(--font-family);
      line-height: 1.6;
      color: var(--text-color);
      background-color: var(--secondary-color);
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 20px 0;
      box-shadow: var(--shadow);
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .logo {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 24px;
      font-weight: 700;
    }

    nav {
      display: flex;
      gap: 20px;
    }

    nav a {
      color: white;
      text-decoration: none;
      font-weight: 500;
      transition: var(--transition);
      padding: 5px 10px;
      border-radius: var(--border-radius);
    }

    nav a:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    .terms-content {
      background: white;
      padding: 40px;
      border-radius: var(--border-radius);
      box-shadow: var(--shadow);
      max-width: 900px;
      margin: 40px auto;
    }

    .terms-content h1 {
      color: var(--primary-color);
      margin-bottom: 30px;
      text-align: center;
    }

    .terms-content h2 {
      color: var(--primary-color);
      margin: 30px 0 15px;
      font-size: 24px;
    }

    .terms-content p {
      margin-bottom: 15px;
    }

    .terms-content ul, .terms-content ol {
      margin-bottom: 15px;
      margin-left: 20px;
    }

    .terms-content li {
      margin-bottom: 8px;
    }

    .terms-content a {
      color: var(--primary-color);
      text-decoration: none;
    }

    .terms-content a:hover {
      text-decoration: underline;
    }

    .back-to-home {
      display: inline-block;
      margin-top: 30px;
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
    }

    .back-to-home:hover {
      text-decoration: underline;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      padding: 30px 0;
      text-align: center;
      margin-top: 40px;
    }

    /* Responsive Styles */
    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 20px;
      }

      nav {
        flex-wrap: wrap;
        justify-content: center;
      }

      .terms-content {
        padding: 20px;
      }
    }
  </style>
</head>
<body>
  <header>
    <div class="container header-content">
      <div class="logo">
        <i class="fa-solid fa-comment-dots"></i>
        <span>ReplyPal</span>
      </div>
      <nav>
        <a href="home.html#about">About</a>
        <a href="home.html#how-to-use">How to Use</a>
        <a href="home.html#features">Features</a>
        <a href="home.html#contact">Contact</a>
        <a href="home.html#policies">Policies</a>
      </nav>
    </div>
  </header>

  <div class="container">
    <div class="terms-content">
      <h1>Terms of Service</h1>
      
      <p>Last Updated: June 1, 2024</p>
      
      <p>Please read these Terms of Service ("Terms", "Terms of Service") carefully before using the ReplyPal Chrome extension and website (the "Service") operated by ReplyPal ("us", "we", or "our").</p>
      
      <p>Your access to and use of the Service is conditioned on your acceptance of and compliance with these Terms. These Terms apply to all visitors, users, and others who access or use the Service.</p>
      
      <p>By accessing or using the Service, you agree to be bound by these Terms. If you disagree with any part of the terms, then you may not access the Service.</p>
      
      <h2>1. Use of Service</h2>
      
      <p>ReplyPal provides an AI-powered email and message response assistant. You may use our Service only as permitted by these Terms and any applicable laws and regulations.</p>
      
      <h2>2. Subscriptions</h2>
      
      <p>Some parts of the Service are billed on a subscription basis. You will be billed in advance on a recurring and periodic basis, depending on the type of subscription plan you select.</p>
      
      <p>At the end of each period, your subscription will automatically renew under the same conditions unless you cancel it or we cancel it.</p>
      
      <p>You may cancel your subscription either through your online account management page or by contacting our customer support team.</p>
      
      <h2>3. Free Trial</h2>
      
      <p>We may, at our sole discretion, offer a subscription with a free trial for a limited period of time.</p>
      
      <p>You may be required to enter your billing information to sign up for the free trial. If you do enter your billing information when signing up for a free trial, we will not charge you until the free trial has expired.</p>
      
      <h2>4. Content</h2>
      
      <p>Our Service allows you to generate content using our AI technology. You are responsible for the content you generate using our Service and any consequences thereof.</p>
      
      <p>You retain all rights to your content. By using our Service to generate content, you grant us a license to access and process your input in order to provide the Service to you.</p>
      
      <h2>5. Privacy</h2>
      
      <p>Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service, to understand our practices.</p>
      
      <h2>6. Limitation of Liability</h2>
      
      <p>In no event shall ReplyPal, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from:</p>
      
      <ol>
        <li>Your access to or use of or inability to access or use the Service;</li>
        <li>Any conduct or content of any third party on the Service;</li>
        <li>Any content obtained from the Service; and</li>
        <li>Unauthorized access, use, or alteration of your transmissions or content.</li>
      </ol>
      
      <h2>7. Disclaimer</h2>
      
      <p>Your use of the Service is at your sole risk. The Service is provided on an "AS IS" and "AS AVAILABLE" basis. The Service is provided without warranties of any kind, whether express or implied.</p>
      
      <h2>8. Changes</h2>
      
      <p>We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days' notice prior to any new terms taking effect.</p>
      
      <h2>9. Contact Us</h2>
      
      <p>If you have any questions about these Terms, please contact <NAME_EMAIL>.</p>
      
      <a href="home.html" class="back-to-home"><i class="fa-solid fa-arrow-left"></i> Back to Home</a>
    </div>
  </div>

  <footer>
    <div class="container">
      <p>&copy; 2024 ReplyPal. All rights reserved.</p>
    </div>
  </footer>
</body>
</html>
