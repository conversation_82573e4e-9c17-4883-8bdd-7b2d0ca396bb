# ReplyPal API Environment Variables

# Application settings
APP_NAME="ReplyPal API"
APP_VERSION="1.0.0"
ENVIRONMENT="development"
LOG_LEVEL="INFO"
CORS_ORIGINS="*"
REQUEST_TIMEOUT=30
MAX_REQUEST_SIZE=10000

# JWT Settings
JWT_SECRET_KEY="your-secret-key-for-jwt-please-change-in-production"

# AI Provider settings
# Choose your AI provider: openai, deepseek, or huggingface
AI_PROVIDER=openai

# Model settings
MODEL_TEMPERATURE=0.7
MODEL_MAX_TOKENS=2000

# OpenAI settings
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_API_MODEL=gpt-3.5-turbo

# DeepSeek settings (if using DeepSeek)
DEEPSEEK_API_KEY=your-deepseek-api-key-here
DEEPSEEK_API_BASE=https://api.deepseek.com/v1
DEEPSEEK_API_MODEL=deepseek-chat

# HuggingFace settings (if using HuggingFace)
HUGGINGFACE_API_KEY=your-huggingface-api-key-here
HUGGINGFACE_MODEL=mistralai/Mistral-7B-Instruct-v0.1

# AWS DynamoDB settings
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
# For local development with DynamoDB Local
# DYNAMODB_ENDPOINT=http://localhost:8000

# DynamoDB table names
DYNAMODB_CUSTOMERS_TABLE=replypal-customers
DYNAMODB_SUBSCRIPTIONS_TABLE=replypal-subscriptions
DYNAMODB_USER_SUBSCRIPTIONS_TABLE=replypal-user-subscriptions
DYNAMODB_USAGE_RECORDS_TABLE=replypal-usage-records

# Stripe settings
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# Stripe price IDs for subscription tiers
STRIPE_BASIC_PRICE_ID=price_basic_id

# Subscription auto-renewal settings
AUTO_RENEWAL_TOKEN_THRESHOLD=1500
AUTO_RENEWAL_TIME_THRESHOLD_DAYS=30

# Frontend URLs for Stripe redirects
FRONTEND_SUCCESS_URL=http://localhost:3000/success
FRONTEND_CANCEL_URL=http://localhost:3000/cancel

# Google OAuth settings
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8000/auth/google-callback
