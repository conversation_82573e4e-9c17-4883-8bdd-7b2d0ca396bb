
import { loginWithGoogle, signOut, getCurrentUser, loadUserFromStorage } from './auth.js';

// DOM Elements
const loginBtn = document.getElementById('loginBtn');
const logoutBtn = document.getElementById('logoutBtn');
const userInfo = document.getElementById('user-info');
const userName = document.getElementById('user-name');

// Check auth state on load
document.addEventListener('DOMContentLoaded', async () => {
  // Load user from storage
  await loadUserFromStorage();
  const user = getCurrentUser();
  updateUIForAuthState(user);
});

// Login Button Handler
loginBtn.addEventListener('click', () => {
  loginWithGoogle((error, user) => {
    if (error) {
      console.error("Login error:", error);
      showToast("Login failed: " + error.message, "error");
    } else {
      showToast(`Welcome ${user.displayName || user.email}!`, "success");
      updateUIForAuthState(user);
    }
  });
});

// Logout Button Handler
logoutBtn.addEventListener('click', () => {
  signOut((error) => {
    if (error) {
      showToast("Logout failed: " + error.message, "error");
    } else {
      showToast("You have been logged out", "info");
      updateUIForAuthState(null);

      // Redirect to login page after a short delay to allow the toast to be visible
      setTimeout(() => {
        window.location.href = 'login.html';
      }, 500);
    }
  });
});

// Update UI based on auth state
function updateUIForAuthState(user) {
  if (user) {
    // User is signed in
    loginBtn.style.display = 'none';
    userInfo.style.display = 'flex';
    userName.textContent = user.displayName || user.email;
  } else {
    // User is signed out
    loginBtn.style.display = 'block';
    userInfo.style.display = 'none';
    userName.textContent = '';
  }
}

// Show toast notification
function showToast(message, type = "info") {
  const toast = document.getElementById('toast');
  toast.textContent = message;
  toast.className = `toast ${type}`;

  // Remove any existing classes and timeouts
  toast.classList.remove('show');
  clearTimeout(toast.dataset.timeout);

  // Force a reflow to restart the animation
  void toast.offsetWidth;

  // Show the toast
  toast.classList.add('show');

  // Set timeout to hide the toast
  const timeout = setTimeout(() => {
    toast.classList.remove('show');
  }, 3000);

  // Store the timeout ID
  toast.dataset.timeout = timeout;
}