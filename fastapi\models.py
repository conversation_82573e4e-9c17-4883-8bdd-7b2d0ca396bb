"""
Database models for ReplyPal API
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Union
from pydantic import BaseModel, Field


class SubscriptionStatus(str, Enum):
    """Subscription status enum"""
    ACTIVE = "active"
    PAST_DUE = "past_due"
    CANCELED = "canceled"
    INCOMPLETE = "incomplete"
    INCOMPLETE_EXPIRED = "incomplete_expired"
    TRIALING = "trialing"
    UNPAID = "unpaid"
    PAUSED = "paused"


class SubscriptionTier(str, Enum):
    """Subscription tier enum"""
    FREE = "free"
    BASIC = "basic"


class UsageLimit(BaseModel):
    """Usage limit model"""
    requests_per_day: int = Field(default=10, description="Maximum number of requests per day")
    max_tokens: int = Field(default=500, description="Maximum tokens per request")


class StripeCustomer(BaseModel):
    """Stripe customer model"""
    id: str = Field(..., description="Stripe customer ID")
    email: Optional[str] = Field(None, description="Customer email")
    name: Optional[str] = Field(None, description="Customer name")
    created: datetime = Field(default_factory=datetime.now, description="Creation timestamp")
    metadata: Dict[str, str] = Field(default_factory=dict, description="Customer metadata")


class StripeSubscription(BaseModel):
    """Stripe subscription model"""
    id: str = Field(..., description="Stripe subscription ID")
    customer_id: str = Field(..., description="Stripe customer ID")
    status: SubscriptionStatus = Field(..., description="Subscription status")
    tier: SubscriptionTier = Field(..., description="Subscription tier")
    current_period_start: datetime = Field(..., description="Current period start timestamp")
    current_period_end: datetime = Field(..., description="Current period end timestamp")
    cancel_at_period_end: bool = Field(default=False, description="Whether subscription cancels at period end")
    created: datetime = Field(default_factory=datetime.now, description="Creation timestamp")
    metadata: Dict[str, str] = Field(default_factory=dict, description="Subscription metadata")


class UserSubscription(BaseModel):
    """User subscription model linking user to Stripe subscription"""
    user_id: str = Field(..., description="User ID")
    stripe_customer_id: str = Field(..., description="Stripe customer ID")
    stripe_subscription_id: Optional[str] = Field(None, description="Stripe subscription ID")
    tier: SubscriptionTier = Field(default=SubscriptionTier.FREE, description="Subscription tier")
    status: SubscriptionStatus = Field(default=SubscriptionStatus.ACTIVE, description="Subscription status")
    usage_limits: UsageLimit = Field(default_factory=UsageLimit, description="Usage limits")
    total_tokens_used: int = Field(default=0, description="Total tokens used since last renewal")
    last_renewal_date: datetime = Field(default_factory=datetime.now, description="Last renewal date")
    created: datetime = Field(default_factory=datetime.now, description="Creation timestamp")
    updated: datetime = Field(default_factory=datetime.now, description="Last update timestamp")


class UsageRecord(BaseModel):
    """Usage record model"""
    user_id: str = Field(..., description="User ID")
    request_type: str = Field(..., description="Type of request")
    tokens_used: int = Field(..., description="Number of tokens used")
    timestamp: datetime = Field(default_factory=datetime.now, description="Usage timestamp")
    metadata: Dict[str, str] = Field(default_factory=dict, description="Usage metadata")
