// User state management
let currentUser = null;

// Load user from storage on init
export function loadUserFromStorage() {
  return new Promise((resolve) => {
    chrome.storage.local.get('user', (data) => {
      currentUser = data.user || null;
      resolve(currentUser);
    });
  });
}

// Initialize by loading user from storage
loadUserFromStorage();

// Get current user
export function getCurrentUser() {
  return currentUser;
}

// Login with Google
export function loginWithGoogle(callback) {
  chrome.runtime.sendMessage({ action: 'authenticateWithGoogle' }, (response) => {
    if (response && response.success) {
      currentUser = response.user;
      callback(null, response.user);
    } else {
      callback(new Error(response?.error || 'Authentication failed'), null);
    }
  });
}

// Sign out
export function signOut(callback) {
  chrome.runtime.sendMessage({ action: 'signOut' }, (response) => {
    if (response && response.success) {
      currentUser = null;
      callback();
    } else {
      callback(new Error(response?.error || 'Sign out failed'));
    }
  });
}
