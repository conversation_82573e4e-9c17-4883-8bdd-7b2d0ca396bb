/* ReplyPal - Main Styles */

:root {
  --primary-color: #4a6cf7;
  --primary-hover: #3a5ce5;
  --primary-light: #eef1ff;
  --secondary-color: #f5f7ff;
  --text-color: #333;
  --light-text: #666;
  --label-text: #444;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --error-color: #f44336;
  --warning-color: #ff9800;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
  --transition: all 0.2s ease;
  --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

/* Reset & Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  overflow: hidden;
  font-family: var(--font-family);
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-color);
  background-color: #fff;
}

.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 400px;
  min-height: 500px;
  resize: none; /* Disable resizing */
  position: relative;
}

/* Header Styles */
.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: var(--primary-color);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;
}

.header-title i {
  font-size: 18px;
  color: #fff !important; /* Force white color */
}

.header-title h1 {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  margin: 0;
}

.header-icons {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

/* Auth Container Styles */
#auth-container {
  margin-bottom: 20px;
}

.auth-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

#user-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.user-info-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--secondary-color);
  padding: 10px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

#user-name {
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tab-icon-btn,
.header-close-btn,
.upgrade-btn {
  background: none;
  border: none;
  color: #fff !important; /* Force white color */
  font-size: 1.3rem;
  padding: 6px 10px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.15s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-icon-btn.active,
.tab-icon-btn:hover,
.header-close-btn:hover,
.upgrade-btn:hover {
  background: rgba(255, 255, 255, 0.18);
}

.tab-icon-btn i,
.header-close-btn i,
.upgrade-btn i {
  color: #fff !important; /* Force white color */
}

/* Upgrade button specific styles */
.upgrade-btn {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  position: relative;
}

.upgrade-btn::after {
  content: '';
  position: absolute;
  top: -3px;
  right: -3px;
  width: 8px;
  height: 8px;
  background-color: #ffcc00;
  border-radius: 50%;
}

/* Main Content */
main {
  flex: 1;
  overflow: auto;
  padding: 15px;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

/* Compose Tab */
#compose {
  width: 100%;
  box-sizing: border-box;
}

.tab-content {
  display: none;
  height: 100%;
  width: 100%; /* Ensure consistent width */
  box-sizing: border-box; /* Include padding in width calculation */
}

.tab-content.active {
  display: block;
}

/* Form Elements */
.form-group {
  margin-bottom: 15px;
  width: 100%;
  position: relative;
}

label {
  display: block;
  margin-bottom: 5px;
  font-size: 12px;
  font-weight: 500;
  color: var(--label-text);
}

.required {
  color: var(--error-color);
}

.info-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: var(--primary-color);
  cursor: help;
}

textarea,
input[type="text"],
select {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-family: var(--font-family);
  font-size: 13px;
  transition: var(--transition);
  background-color: #fff;
}

textarea {
  resize: both;
  min-height: 60px;
  width: 100%;
  max-width: none;
  overflow: auto;
  position: relative;
}

textarea:focus,
input[type="text"]:focus,
select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.1);
}

textarea[readonly] {
  background-color: var(--secondary-color);
  cursor: default;
}

/* Controls Row */
.controls-row {
  display: flex;
  align-items: flex-end;
  gap: 10px;
  margin-bottom: 15px;
}

.control-group {
  flex: 1;
}

.generate-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.control-label {
  font-size: 11px;
  margin-bottom: 3px;
}

.control-select {
  height: 32px;
  padding: 0 8px;
  font-size: 12px;
}

/* Buttons */
.primary-btn,
.secondary-btn,
.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  padding: 6px 12px;
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  font-family: var(--font-family);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.primary-btn {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-hover);
  height: 32px;
}

.primary-btn:hover {
  background-color: var(--primary-hover);
}

.primary-btn:disabled,
.primary-btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #a0a0a0;
  border-color: #909090;
}

.secondary-btn {
  background-color: var(--secondary-color);
  color: var(--text-color);
  border-color: var(--border-color);
}

.secondary-btn:hover {
  background-color: #e8eaff;
}

.action-btn {
  background-color: transparent;
  color: var(--primary-color);
  border-color: transparent;
  padding: 4px 8px;
  font-size: 11px;
}

.action-btn:hover {
  background-color: var(--primary-light);
}

.action-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Response Container */
.response-container {
  margin-top: 18px;
  margin-bottom: 8px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  padding: 0 0 12px 0;
  border: 1px solid #e0e0e0;
}

.response-header {
  padding: 8px 12px 0 12px;
  font-weight: 600;
  color: #4a6cf7;
}

.response-box {
  background: #f8fafc;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  margin: 8px 12px 0 12px;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
  color: #222;
  min-height: 60px;
  width: calc(100% - 24px);
  box-sizing: border-box;
  resize: vertical;
}

/* History Styles */
#history {
  width: 100%;
  box-sizing: border-box;
}

.history-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.history-header h3 {
  font-size: 14px;
  font-weight: 600;
}

.history-list {
  flex: 1;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 10px;
}

.empty-message {
  color: var(--light-text);
  text-align: center;
  padding: 20px;
}

.history-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: #fff;
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.history-item-title {
  font-weight: 500;
}

.history-item-date {
  font-size: 11px;
  color: var(--light-text);
}

.history-item-content {
  margin-bottom: 10px;
  font-size: 12px;
  color: var(--light-text);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-item-actions {
  display: flex;
  gap: 5px;
  justify-content: flex-end;
}

/* Settings Styles */
#settings {
  width: 100%;
  box-sizing: border-box;
}

.settings-container {
  width: 100%; /* Use full width of the container */
  box-sizing: border-box; /* Include padding in width calculation */
}

.settings-container h3 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 15px;
}

.setting-info {
  font-size: 12px;
  color: var(--light-text);
  margin-top: 5px;
}

/* Usage Meter Styles */
.usage-info {
  margin: 10px 0;
}

.usage-meter {
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.usage-progress {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 4px;
  width: 0%;
  transition: width 0.3s ease;
}

#usage-text {
  font-size: 13px;
  color: var(--text-color);
  margin-bottom: 5px;
}

#upgrade-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

#upgrade-link:hover {
  text-decoration: underline;
}

.setting-result {
  margin-top: 10px;
  font-size: 12px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.hotkeys-info {
  margin: 20px 0;
  padding: 15px;
  background-color: var(--secondary-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.hotkeys-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.hotkeys-info h3 {
  margin-bottom: 0;
  font-size: 14px;
  color: var(--primary-color);
}

.toggle-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-btn:hover {
  background-color: rgba(74, 108, 247, 0.1);
}

.toggle-btn i {
  transition: transform 0.3s ease;
}

.toggle-btn.expanded i {
  transform: rotate(180deg);
}

.hotkey {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.primary-hotkey {
  background-color: rgba(74, 108, 247, 0.05);
  padding: 8px 10px;
  border-radius: 6px;
  margin-bottom: 12px;
}

.additional-hotkeys {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px dashed rgba(0, 0, 0, 0.1);
  transition: max-height 0.3s ease, opacity 0.3s ease;
  overflow: hidden;
  max-height: 500px;
  opacity: 1;
}

.additional-hotkeys.hidden {
  max-height: 0;
  opacity: 0;
  margin-top: 0;
  padding-top: 0;
  border-top: none;
}

.hotkey:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.key-combo {
  display: flex;
  align-items: center;
  gap: 4px;
}

.key-desc {
  color: var(--light-text);
  font-size: 12px;
}

kbd {
  display: inline-block;
  padding: 3px 6px;
  font-size: 11px;
  font-family: monospace;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  color: var(--primary-color);
}

/* Utility Classes */
.hidden {
  display: none !important;
}

/* Exception for additional-hotkeys which uses max-height animation */
.additional-hotkeys.hidden {
  display: block !important;
}

/* Toast Notification */
.toast {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%) translateY(100px);
  padding: 10px 15px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: var(--border-radius);
  font-size: 12px;
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
  z-index: 1000;
}

.toast.show {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}

.toast.error {
  background-color: var(--error-color);
}

.toast.success {
  background-color: var(--success-color);
}

.toast.info {
  background-color: var(--primary-color);
}

/* Compose Tab Usage Indicators */
.compose-usage-counter {
  margin-left: 10px;
  font-size: 12px;
  color: var(--light-text);
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  background-color: var(--secondary-color);
  border-radius: 4px;
}

/* Compact usage indicator above the generate button */
.compact-usage-indicator {
  font-size: 9px;
  color: var(--light-text);
  display: block;
  text-align: center;
  margin-bottom: 5px;
  padding: 3px 6px;
  border-radius: 4px;
  white-space: nowrap;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  background-color: var(--secondary-color);
  border: 1px solid var(--border-color);
}

.compact-usage-indicator:hover {
  color: var(--primary-color);
}

.compact-usage-indicator.warning {
  color: var(--warning-color);
  background-color: #fff9e6;
  border-color: #ffe0a6;
}

.compact-usage-indicator.danger {
  color: var(--error-color);
  background-color: #fff8f8;
  border-color: #ffdddd;
}

.compose-limit-indicator {
  margin-left: 10px;
  font-size: 12px;
  color: var(--error-color);
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 4px 8px;
  background-color: #fff8f8;
  border: 1px solid #ffdddd;
  border-radius: 4px;
  cursor: pointer;
}

.compose-limit-indicator:hover {
  background-color: #ffeeee;
}

/* Usage Limit Message */
.usage-limit-message {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: var(--border-radius);
  padding: 15px;
  margin-bottom: 15px;
  text-align: center;
}

.usage-limit-message h3 {
  color: var(--error-color);
  margin-bottom: 10px;
  font-size: 16px;
}

.usage-limit-message p {
  margin-bottom: 10px;
  font-size: 14px;
}

.usage-limit-message ul {
  text-align: left;
  margin: 15px auto;
  max-width: 300px;
  padding-left: 20px;
}

.usage-limit-message li {
  margin-bottom: 5px;
  font-size: 13px;
}

.usage-limit-message button {
  margin-top: 10px;
  padding: 10px 20px;
  font-size: 14px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: background-color 0.2s;
}

.usage-limit-message button:hover {
  background-color: var(--primary-hover);
}

/* Error Message */
.error-message {
  background-color: #fff8f8;
  border: 1px solid #ffdddd;
  border-radius: var(--border-radius);
  padding: 15px;
  margin-bottom: 15px;
  text-align: center;
}

.error-message h3 {
  color: var(--error-color);
  margin-bottom: 10px;
  font-size: 16px;
}

.error-message p {
  margin-bottom: 15px;
  font-size: 14px;
  color: #555;
}

.error-message button {
  margin-top: 10px;
  padding: 8px 16px;
  font-size: 14px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: background-color 0.2s;
}

.error-message button:hover {
  background-color: var(--primary-hover);
}
