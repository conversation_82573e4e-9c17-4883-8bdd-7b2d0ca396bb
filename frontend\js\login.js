/**
 * ReplyPal - Login Script
 * Handles user authentication using Chrome Identity API
 */

document.addEventListener('DOMContentLoaded', () => {
  const loginBtn = document.getElementById('loginBtn');
  
  // Check if user is already logged in
  chrome.storage.local.get('user', (data) => {
    if (data.user) {
      // User is already logged in, redirect to main page
      redirectToMainPage();
    }
  });
  
  // Login button click handler
  loginBtn.addEventListener('click', () => {
    loginBtn.disabled = true;
    loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';
    
    // Use Chrome Identity API for authentication
    chrome.runtime.sendMessage({ action: 'authenticateWithGoogle' }, (response) => {
      if (response && response.success) {
        showToast(`Welcome ${response.user.name || response.user.email}!`, 'success');
        
        // Short delay before redirecting to main page
        setTimeout(() => {
          redirectToMainPage();
        }, 1000);
      } else {
        showToast('Login failed: ' + (response?.error || 'Unknown error'), 'error');
        loginBtn.disabled = false;
        loginBtn.innerHTML = '<i class="fab fa-google"></i> Login with Google';
      }
    });
  });
  
  /**
   * Redirect to main page
   */
  function redirectToMainPage() {
    window.location.href = 'index.html';
  }
  
  /**
   * Show toast notification
   * @param {string} message - Message to display
   * @param {string} type - Toast type (info, success, error)
   */
  function showToast(message, type = 'info') {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.className = `toast ${type}`;
    
    // Remove any existing classes and timeouts
    toast.classList.remove('show');
    clearTimeout(toast.dataset.timeout);
    
    // Force a reflow to restart the animation
    void toast.offsetWidth;
    
    // Show the toast
    toast.classList.add('show');
    
    // Set timeout to hide the toast
    const timeout = setTimeout(() => {
      toast.classList.remove('show');
    }, 3000);
    
    // Store the timeout ID
    toast.dataset.timeout = timeout;
  }
});
